#!/usr/bin/env python3
"""
Script de test pour l'authentification avec différents rôles
"""

import requests
import json
from typing import Dict, Any

# Configuration
API_BASE_URL = "http://localhost:8000/api"
AUTH_ENDPOINT = f"{API_BASE_URL}/auth/token/"
PROFILE_ENDPOINT = f"{API_BASE_URL}/account/profile/"
STATS_ENDPOINT = f"{API_BASE_URL}/stats/dashboard/"

# Utilisateurs de test selon la migration
TEST_USERS = [
    {
        "email": "<EMAIL>",
        "password": "admin123",
        "role": "Administrateur",
        "name": "Admin System"
    },
    {
        "email": "<EMAIL>", 
        "password": "password123",
        "role": "Responsable Régional",
        "name": "<PERSON>"
    },
    {
        "email": "<EMAIL>",
        "password": "password123", 
        "role": "<PERSON> <PERSON><PERSON>",
        "name": "<PERSON>"
    },
    {
        "email": "<EMAIL>",
        "password": "password123",
        "role": "Agent Analyste", 
        "name": "<PERSON><PERSON><PERSON> Expert"
    },
    {
        "email": "<EMAIL>",
        "password": "password123",
        "role": "Agent Technique",
        "name": "Technicien Support"
    }
]

def test_login(email: str, password: str) -> Dict[str, Any]:
    """Test de connexion pour un utilisateur"""
    try:
        response = requests.post(AUTH_ENDPOINT, json={
            "email": email,
            "password": password
        })
        
        if response.status_code == 200:
            return {
                "success": True,
                "data": response.json(),
                "status_code": response.status_code
            }
        else:
            return {
                "success": False,
                "error": response.text,
                "status_code": response.status_code
            }
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "status_code": None
        }

def test_profile_access(token: str) -> Dict[str, Any]:
    """Test d'accès au profil utilisateur"""
    try:
        headers = {"Authorization": f"Bearer {token}"}
        response = requests.get(PROFILE_ENDPOINT, headers=headers)
        
        if response.status_code == 200:
            return {
                "success": True,
                "data": response.json(),
                "status_code": response.status_code
            }
        else:
            return {
                "success": False,
                "error": response.text,
                "status_code": response.status_code
            }
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "status_code": None
        }

def test_stats_access(token: str) -> Dict[str, Any]:
    """Test d'accès aux statistiques (selon permissions)"""
    try:
        headers = {"Authorization": f"Bearer {token}"}
        response = requests.get(STATS_ENDPOINT, headers=headers)
        
        if response.status_code == 200:
            return {
                "success": True,
                "data": response.json(),
                "status_code": response.status_code
            }
        else:
            return {
                "success": False,
                "error": response.text,
                "status_code": response.status_code
            }
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "status_code": None
        }

def print_separator(title: str):
    """Affiche un séparateur avec titre"""
    print("\n" + "="*60)
    print(f" {title}")
    print("="*60)

def print_user_test_result(user: Dict[str, str], login_result: Dict[str, Any], 
                          profile_result: Dict[str, Any], stats_result: Dict[str, Any]):
    """Affiche les résultats de test pour un utilisateur"""
    print(f"\n🧪 Test pour: {user['name']} ({user['role']})")
    print(f"   Email: {user['email']}")
    
    # Test de connexion
    if login_result["success"]:
        print("   ✅ Connexion: SUCCÈS")
        token = login_result["data"].get("access", "")
        print(f"   🔑 Token reçu: {token[:20]}...")
    else:
        print(f"   ❌ Connexion: ÉCHEC ({login_result['status_code']})")
        print(f"      Erreur: {login_result['error']}")
        return
    
    # Test d'accès au profil
    if profile_result["success"]:
        print("   ✅ Profil: ACCESSIBLE")
        profile_data = profile_result["data"]
        authorities = profile_data.get("authorities", [])
        primary_authority = profile_data.get("primary_authority", "Non défini")
        print(f"      Autorité principale: {primary_authority}")
        print(f"      Toutes les autorités: {authorities}")
    else:
        print(f"   ❌ Profil: INACCESSIBLE ({profile_result['status_code']})")
    
    # Test d'accès aux statistiques
    if stats_result["success"]:
        print("   ✅ Statistiques: ACCESSIBLE")
        stats_data = stats_result["data"]
        print(f"      Détections totales: {stats_data.get('total_detections', 'N/A')}")
        print(f"      Alertes actives: {stats_data.get('active_alerts', 'N/A')}")
    else:
        print(f"   ❌ Statistiques: INACCESSIBLE ({stats_result['status_code']})")
        if stats_result['status_code'] == 403:
            print("      (Normal pour certains rôles - permissions insuffisantes)")

def main():
    """Fonction principale de test"""
    print_separator("TEST D'AUTHENTIFICATION - GOLDSENTINEL")
    print("🚀 Démarrage des tests d'authentification avec différents rôles...")
    
    # Vérifier que le serveur est accessible
    try:
        response = requests.get(f"{API_BASE_URL}/")
        print(f"✅ Serveur API accessible sur {API_BASE_URL}")
    except Exception as e:
        print(f"❌ Impossible d'accéder au serveur API: {e}")
        print("💡 Assurez-vous que le serveur Django est démarré avec: python manage.py runserver")
        return
    
    print_separator("TESTS PAR UTILISATEUR")
    
    # Tester chaque utilisateur
    for user in TEST_USERS:
        # Test de connexion
        login_result = test_login(user["email"], user["password"])
        
        # Si connexion réussie, tester les accès
        profile_result = {"success": False, "error": "Connexion échouée", "status_code": None}
        stats_result = {"success": False, "error": "Connexion échouée", "status_code": None}
        
        if login_result["success"]:
            token = login_result["data"].get("access", "")
            profile_result = test_profile_access(token)
            stats_result = test_stats_access(token)
        
        # Afficher les résultats
        print_user_test_result(user, login_result, profile_result, stats_result)
    
    print_separator("RÉSUMÉ DES PERMISSIONS ATTENDUES")
    print("""
📋 Permissions par rôle (selon l'implémentation):

🔴 Administrateur:
   - ✅ Connexion, Profil, Statistiques, Toutes fonctionnalités

🟠 Responsable Régional:
   - ✅ Connexion, Profil, Statistiques
   - ✅ Gestion investigations, Risques financiers
   - ✅ Assignation alertes

🟡 Agent Analyste:
   - ✅ Connexion, Profil, Statistiques (limitées)
   - ✅ Validation détections, Lancement analyses
   - ❌ Risques financiers

🟢 Agent Technique:
   - ✅ Connexion, Profil, Statistiques (techniques)
   - ✅ Logs système
   - ❌ Gestion investigations

🔵 Agent Terrain:
   - ✅ Connexion, Profil
   - ✅ Investigations assignées
   - ❌ Statistiques, Analyses
    """)
    
    print_separator("INSTRUCTIONS POUR TESTER LE FRONTEND")
    print("""
🌐 Pour tester l'interface frontend:

1. Démarrez le serveur frontend:
   cd frontend && npm run dev

2. Ouvrez http://localhost:5173 dans votre navigateur

3. Testez la connexion avec ces comptes:

   👑 ADMINISTRATEUR:
   Email: <EMAIL>
   Mot de passe: admin123

   👔 RESPONSABLE RÉGIONAL:
   Email: <EMAIL>  
   Mot de passe: password123

   🔍 AGENT ANALYSTE:
   Email: <EMAIL>
   Mot de passe: password123

   🛠️ AGENT TECHNIQUE:
   Email: <EMAIL>
   Mot de passe: password123

   🚶 AGENT TERRAIN:
   Email: <EMAIL>
   Mot de passe: password123

4. Vérifiez que:
   - Le dashboard s'adapte selon le rôle
   - La navigation filtre les pages autorisées
   - Les actions rapides correspondent aux permissions
   - Le profil affiche correctement les autorités
    """)

if __name__ == "__main__":
    main()
