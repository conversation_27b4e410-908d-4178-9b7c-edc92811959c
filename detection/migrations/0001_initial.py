# Generated by Django 5.1.4 on 2025-05-28 07:41

import django.contrib.gis.db.models.fields
import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('account', '0001_initial'),
        ('image', '0001_initial'),
        ('region', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='DetectionModel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('latitude', models.FloatField()),
                ('longitude', models.FloatField()),
                ('coordinate_precision', models.CharField(max_length=50)),
                ('detection_type', models.Char<PERSON>ield(choices=[('MINING_SITE', 'Mining Site'), ('WATER_POLLUTION', 'Water Pollution'), ('ACCESS_ROAD', 'Access Road'), ('EQUIPMENT', 'Mining Equipment')], max_length=30)),
                ('confidence_score', models.FloatField(help_text='AI confidence 0.0-1.0')),
                ('area_hectares', models.FloatField(help_text='Estimated area in hectares')),
                ('zone_geometry', django.contrib.gis.db.models.fields.PolygonField(srid=4326)),
                ('center_point', django.contrib.gis.db.models.fields.PointField(srid=4326)),
                ('algorithm_version', models.CharField(default='Ghana_v3.7', max_length=50)),
                ('processing_time_seconds', models.FloatField(blank=True, null=True)),
                ('validation_status', models.CharField(choices=[('DETECTED', 'Detected'), ('VALIDATED', 'Validated'), ('FALSE_POSITIVE', 'False Positive'), ('CONFIRMED', 'Confirmed')], default='DETECTED', max_length=20)),
                ('validated_at', models.DateTimeField(blank=True, null=True)),
                ('detection_date', models.DateTimeField(auto_now_add=True)),
                ('image', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='image.imagemodel')),
                ('region', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='region.regionmodel')),
                ('validated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='account.usermodel')),
            ],
            options={
                'db_table': 'mining_detections',
                'ordering': ['-detection_date'],
                'indexes': [models.Index(fields=['confidence_score'], name='mining_dete_confide_d24e0e_idx'), models.Index(fields=['detection_type'], name='mining_dete_detecti_a77df8_idx'), models.Index(fields=['region', 'detection_date'], name='mining_dete_region__27194a_idx')],
            },
        ),
    ]
