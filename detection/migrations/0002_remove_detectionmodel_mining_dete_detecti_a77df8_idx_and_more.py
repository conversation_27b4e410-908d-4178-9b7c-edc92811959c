# Generated by Django 5.2.1 on 2025-05-29 13:37

import django.contrib.gis.db.models.fields
import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('account', '0002_remove_usermodel_access_level_and_more'),
        ('detection', '0001_initial'),
        ('image', '0002_remove_imagemodel_image_file_and_more'),
        ('region', '0001_initial'),
    ]

    operations = [
        migrations.RemoveIndex(
            model_name='detectionmodel',
            name='mining_dete_detecti_a77df8_idx',
        ),
        migrations.RenameField(
            model_name='detectionmodel',
            old_name='processing_time_seconds',
            new_name='ndti_anomaly_score',
        ),
        migrations.RemoveField(
            model_name='detectionmodel',
            name='center_point',
        ),
        migrations.RemoveField(
            model_name='detectionmodel',
            name='coordinate_precision',
        ),
        migrations.AddField(
            model_name='detectionmodel',
            name='ndvi_anomaly_score',
            field=models.FloatField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='detectionmodel',
            name='ndwi_anomaly_score',
            field=models.FloatField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='detectionmodel',
            name='algorithm_version',
            field=models.CharField(default='NDVI_NDWI_NDTI_v1.0', max_length=50),
        ),
        migrations.AlterField(
            model_name='detectionmodel',
            name='area_hectares',
            field=models.FloatField(default=0, help_text='Surface estimée en hectares'),
        ),
        migrations.AlterField(
            model_name='detectionmodel',
            name='confidence_score',
            field=models.FloatField(help_text='Score de confiance 0.0-1.0'),
        ),
        migrations.AlterField(
            model_name='detectionmodel',
            name='detection_type',
            field=models.CharField(choices=[('MINING_SITE', 'Site minier'), ('WATER_POLLUTION', 'Pollution eau'), ('DEFORESTATION', 'Déforestation'), ('SOIL_DISTURBANCE', 'Perturbation sol')], max_length=30),
        ),
        migrations.AlterField(
            model_name='detectionmodel',
            name='image',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='detections', to='image.imagemodel'),
        ),
        migrations.AlterField(
            model_name='detectionmodel',
            name='validation_status',
            field=models.CharField(choices=[('DETECTED', 'Détecté'), ('VALIDATED', 'Validé'), ('FALSE_POSITIVE', 'Faux positif'), ('CONFIRMED', 'Confirmé')], default='DETECTED', max_length=20),
        ),
        migrations.AlterField(
            model_name='detectionmodel',
            name='zone_geometry',
            field=django.contrib.gis.db.models.fields.PolygonField(blank=True, null=True, srid=4326),
        ),
        migrations.AddIndex(
            model_name='detectionmodel',
            index=models.Index(fields=['detection_type', 'validation_status'], name='mining_dete_detecti_b6ec7a_idx'),
        ),
    ]
