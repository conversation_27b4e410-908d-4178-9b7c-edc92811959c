# Generated by Django 5.2.1 on 2025-05-29 15:25

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('account', '0002_remove_usermodel_access_level_and_more'),
        ('detection', '0002_remove_detectionmodel_mining_dete_detecti_a77df8_idx_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='InvestigationModel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('target_coordinates', models.CharField(max_length=50)),
                ('access_instructions', models.TextField(blank=True)),
                ('status', models.CharField(choices=[('PENDING', 'En attente'), ('ASSIGNED', 'Assignée'), ('IN_PROGRESS', 'En cours'), ('COMPLETED', 'Terminée')], default='PENDING', max_length=20)),
                ('result', models.CharField(blank=True, choices=[('CONFIRMED', 'Confirmé'), ('FALSE_POSITIVE', 'Faux positif'), ('NEEDS_MONITORING', 'Surveillance nécessaire')], max_length=20, null=True)),
                ('field_notes', models.TextField(blank=True)),
                ('investigation_date', models.DateField(blank=True, null=True)),
                ('assigned_to', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='account.usermodel')),
                ('detection', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to='detection.detectionmodel')),
            ],
            options={
                'verbose_name': 'Investigation',
                'verbose_name_plural': 'Investigations',
                'db_table': 'investigations',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='DetectionFeedbackModel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('original_confidence', models.FloatField()),
                ('original_ndvi_score', models.FloatField()),
                ('original_ndwi_score', models.FloatField()),
                ('original_ndti_score', models.FloatField()),
                ('ground_truth_confirmed', models.BooleanField()),
                ('agent_confidence', models.IntegerField(choices=[(1, 'Faible'), (2, 'Moyen'), (3, 'Élevé')])),
                ('used_for_training', models.BooleanField(default=False)),
                ('detection', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to='detection.detectionmodel')),
                ('investigation', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to='detection.investigationmodel')),
            ],
            options={
                'verbose_name': 'Detection Feedback',
                'verbose_name_plural': 'Detection Feedbacks',
                'db_table': 'detection_feedbacks',
                'ordering': ['-created_at'],
            },
        ),
        migrations.AddIndex(
            model_name='investigationmodel',
            index=models.Index(fields=['status', 'assigned_to'], name='investigati_status_00ec1b_idx'),
        ),
        migrations.AddIndex(
            model_name='investigationmodel',
            index=models.Index(fields=['detection', 'status'], name='investigati_detecti_94d4b4_idx'),
        ),
        migrations.AddIndex(
            model_name='detectionfeedbackmodel',
            index=models.Index(fields=['ground_truth_confirmed', 'used_for_training'], name='detection_f_ground__e871b1_idx'),
        ),
    ]
