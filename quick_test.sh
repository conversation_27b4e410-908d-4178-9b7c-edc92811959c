#!/bin/bash

# Script de test rapide pour GoldSentinel
# Lance les serveurs et ouvre la page de connexion

set -e

echo "🚀 Test Rapide GoldSentinel - Page de Connexion"
echo "==============================================="

# Couleurs
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Vérifier la structure du projet
if [ ! -d "backend" ] || [ ! -d "frontend" ]; then
    print_error "Structure de projet incorrecte. Exécutez depuis la racine du projet."
    exit 1
fi

print_status "Vérification des prérequis..."

# Vérifier Node.js
if ! command -v node &> /dev/null; then
    print_error "Node.js non installé"
    exit 1
fi

# Vérifier Python
if ! command -v python &> /dev/null && ! command -v python3 &> /dev/null; then
    print_error "Python non installé"
    exit 1
fi

print_success "Prérequis OK"

# Fonction pour démarrer le backend
start_backend() {
    print_status "Démarrage du backend Django..."
    cd backend
    
    # Activer l'environnement virtuel si disponible
    if [ -d "venv" ]; then
        source venv/bin/activate
    elif [ -d "../venv" ]; then
        source ../venv/bin/activate
    fi
    
    # Appliquer les migrations si nécessaire
    python manage.py migrate --run-syncdb > /dev/null 2>&1 || true
    
    # Démarrer le serveur en arrière-plan
    python manage.py runserver > /dev/null 2>&1 &
    BACKEND_PID=$!
    
    cd ..
    
    # Attendre que le backend soit prêt
    print_status "Attente du démarrage du backend..."
    for i in {1..10}; do
        if curl -s http://localhost:8000/api/ > /dev/null 2>&1; then
            print_success "Backend démarré sur http://localhost:8000"
            return 0
        fi
        sleep 1
    done
    
    print_error "Impossible de démarrer le backend"
    return 1
}

# Fonction pour démarrer le frontend
start_frontend() {
    print_status "Démarrage du frontend React..."
    cd frontend
    
    # Installer les dépendances si nécessaire
    if [ ! -d "node_modules" ]; then
        print_status "Installation des dépendances npm..."
        npm install > /dev/null 2>&1
    fi
    
    # Démarrer le serveur en arrière-plan
    npm run dev > /dev/null 2>&1 &
    FRONTEND_PID=$!
    
    cd ..
    
    # Attendre que le frontend soit prêt
    print_status "Attente du démarrage du frontend..."
    for i in {1..15}; do
        if curl -s http://localhost:5173 > /dev/null 2>&1; then
            print_success "Frontend démarré sur http://localhost:5173"
            return 0
        fi
        sleep 1
    done
    
    print_error "Impossible de démarrer le frontend"
    return 1
}

# Fonction de nettoyage
cleanup() {
    print_status "Arrêt des serveurs..."
    if [ ! -z "$BACKEND_PID" ]; then
        kill $BACKEND_PID > /dev/null 2>&1 || true
    fi
    if [ ! -z "$FRONTEND_PID" ]; then
        kill $FRONTEND_PID > /dev/null 2>&1 || true
    fi
    print_success "Serveurs arrêtés"
}

# Capturer Ctrl+C pour nettoyer
trap cleanup EXIT

# Démarrer les serveurs
if start_backend && start_frontend; then
    print_success "Serveurs démarrés avec succès !"
    echo ""
    echo "🌐 URLs d'accès:"
    echo "   Frontend: http://localhost:5173"
    echo "   Backend:  http://localhost:8000/api"
    echo ""
    echo "👥 Comptes de test disponibles:"
    echo "   👑 Administrateur:        <EMAIL> / admin123"
    echo "   👔 Responsable Régional:  <EMAIL> / password123"
    echo "   🔍 Agent Analyste:       <EMAIL> / password123"
    echo "   🛠️ Agent Technique:       <EMAIL> / password123"
    echo "   🚶 Agent Terrain:        <EMAIL> / password123"
    echo ""
    
    # Ouvrir le navigateur automatiquement
    if command -v xdg-open &> /dev/null; then
        print_status "Ouverture du navigateur..."
        xdg-open http://localhost:5173 > /dev/null 2>&1 &
    elif command -v open &> /dev/null; then
        print_status "Ouverture du navigateur..."
        open http://localhost:5173 > /dev/null 2>&1 &
    else
        print_warning "Impossible d'ouvrir automatiquement le navigateur"
        print_status "Ouvrez manuellement: http://localhost:5173"
    fi
    
    echo ""
    print_status "🧪 Instructions de test:"
    echo "   1. La page de connexion devrait s'ouvrir automatiquement"
    echo "   2. Cliquez sur 'Comptes de test' pour voir les options"
    echo "   3. Cliquez sur un compte pour remplir automatiquement"
    echo "   4. Testez la connexion avec différents rôles"
    echo "   5. Vérifiez que le dashboard s'adapte selon les permissions"
    echo ""
    print_warning "Appuyez sur Ctrl+C pour arrêter les serveurs"
    
    # Attendre indéfiniment
    while true; do
        sleep 1
    done
else
    print_error "Échec du démarrage des serveurs"
    exit 1
fi
