{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "@mui/icons-material": "^7.1.0", "@mui/material": "^7.1.0", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-toast": "^1.1.5", "@tailwindcss/postcss7-compat": "^2.2.17", "@tanstack/react-query": "^5.79.0", "@types/leaflet-draw": "^1.0.12", "axios": "^1.9.0", "chart.js": "^4.4.1", "chartjs-adapter-date-fns": "^3.0.0", "date-fns": "^3.6.0", "framer-motion": "^11.0.8", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "leaflet": "^1.9.4", "leaflet-draw": "^1.0.4", "react": "^19.1.0", "react-chartjs-2": "^5.2.0", "react-dom": "^19.1.0", "react-hot-toast": "^2.4.1", "react-leaflet": "^5.0.0", "react-router-dom": "^7.6.1", "recharts": "^2.15.3"}, "devDependencies": {"@eslint/js": "^9.25.0", "@tailwindcss/forms": "^0.5.10", "@types/leaflet": "^1.9.18", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.14", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "postcss": "^8.4.31", "tailwindcss": "^3.3.0", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5"}}