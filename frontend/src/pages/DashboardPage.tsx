import React, { useEffect, useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { motion } from 'framer-motion';
import {
  ExclamationTriangleIcon,
  ChartBarIcon,
  DocumentTextIcon,
  UsersIcon,
  ClockIcon,
  BanknotesIcon,
  ShieldCheckIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon,
  CameraIcon,
  MagnifyingGlassIcon,
  MapPinIcon,
  ChartPieIcon,
  BeakerIcon,
  PlayIcon,
  EyeIcon,
} from '@heroicons/react/24/outline';
import { useAuth } from '../contexts/AuthContext';
import { usePermissions } from '../hooks/usePermissions';
import statsService from '../services/stats.service';
import detectionService from '../services/detection.service';
import type { DashboardStats } from '../services/stats.service';
import { StatCard, Card, Button, Loading } from '../components/ui';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>hnut<PERSON><PERSON> } from '../components/charts/ChartComponents';
import { BondoukouMap } from '../components/maps/BondoukouMap';
import toast from 'react-hot-toast';

interface StatCardData {
  title: string;
  value: number;
  icon: React.ElementType;
  color: string;
  trend?: {
    value: number;
    isPositive: boolean;
  };
}

export const DashboardPage: React.FC = () => {
  const { user } = useAuth();
  const permissions = usePermissions();
  const [refreshInterval, setRefreshInterval] = useState<NodeJS.Timeout | null>(null);

  // Fetch dashboard statistics avec refresh automatique
  const {
    data: dashboardStats,
    isLoading: isLoadingStats,
    error: statsError,
    refetch: refetchStats
  } = useQuery<DashboardStats, Error>({
    queryKey: ['dashboardStats'],
    queryFn: () => statsService.getDashboardStats(),
    refetchInterval: 30000, // Refresh toutes les 30 secondes
    refetchIntervalInBackground: true,
    retry: 3,
    retryDelay: 1000,
  });

  // Fetch détections pour la carte
  const {
    data: detectionsData,
    isLoading: isLoadingDetections
  } = useQuery({
    queryKey: ['detections-for-map'],
    queryFn: () => detectionService.getDetections({
      limit: 50,
      ordering: '-detection_date',
      validation_status: 'VALIDATED,CONFIRMED'
    }),
    refetchInterval: 60000, // Refresh toutes les minutes
    enabled: permissions.canViewStats(),
  });

  // Fetch tendances détections
  const {
    data: detectionTrends
  } = useQuery({
    queryKey: ['detection-trends'],
    queryFn: () => statsService.getDetectionTrends(30),
    refetchInterval: 300000, // Refresh toutes les 5 minutes
    enabled: permissions.canViewStats(),
  });

  // Fetch impact financier
  const {
    data: financialImpact
  } = useQuery({
    queryKey: ['financial-impact'],
    queryFn: () => statsService.getFinancialImpact(),
    refetchInterval: 300000, // Refresh toutes les 5 minutes
    enabled: permissions.canViewStats(),
  });

  // Gestion des erreurs
  useEffect(() => {
    if (statsError) {
      toast.error('Erreur lors du chargement des statistiques');
      console.error('Erreur stats:', statsError);
    }
  }, [statsError]);

  // Statistiques en fonction du rôle avec vraies données backend
  const getStats = (): StatCardData[] => {
    if (isLoadingStats || !dashboardStats) {
      return [];
    }

    const stats: StatCardData[] = [];
    const {
      total_detections,
      active_alerts,
      pending_investigations,
      total_financial_risk,
      accuracy_rate,
      high_confidence_detections,
    } = dashboardStats;

    // 6 cartes KPI principales selon spécifications
    if (permissions.canViewStats()) {
      stats.push(
        {
          title: 'Total Détections Bondoukou',
          value: total_detections,
          icon: MagnifyingGlassIcon,
          color: 'bg-gradient-to-r from-blue-500 to-blue-600',
          trend: {
            value: detectionTrends?.trend_analysis ? 12.5 : 0,
            isPositive: true
          },
        },
        {
          title: 'Alertes Actives',
          value: active_alerts,
          icon: ExclamationTriangleIcon,
          color: 'bg-gradient-to-r from-red-500 to-red-600',
          trend: { value: 8.2, isPositive: false },
        },
        {
          title: 'Sites Confirmés',
          value: high_confidence_detections,
          icon: ShieldCheckIcon,
          color: 'bg-gradient-to-r from-green-500 to-green-600',
          trend: { value: 15.3, isPositive: true },
        },
        {
          title: 'Impact Financier (FCFA)',
          value: Math.round(total_financial_risk),
          icon: BanknotesIcon,
          color: 'bg-gradient-to-r from-amber-500 to-orange-500',
          trend: { value: 5.7, isPositive: false },
        },
        {
          title: 'Investigations En Cours',
          value: pending_investigations,
          icon: DocumentTextIcon,
          color: 'bg-gradient-to-r from-indigo-500 to-indigo-600',
          trend: { value: 10.2, isPositive: true },
        },
        {
          title: 'Précision Système IA (%)',
          value: Math.round(accuracy_rate * 100),
          icon: ChartBarIcon,
          color: 'bg-gradient-to-r from-purple-500 to-purple-600',
          trend: { value: 2.1, isPositive: true },
        }
      );
    } else if (permissions.isAgentTerrain()) {
      // Statistiques simplifiées pour agents terrain
      stats.push(
        {
          title: 'Alertes Assignées',
          value: active_alerts,
          icon: ExclamationTriangleIcon,
          color: 'bg-gradient-to-r from-red-500 to-red-600',
        },
        {
          title: 'Investigations En Cours',
          value: pending_investigations,
          icon: DocumentTextIcon,
          color: 'bg-gradient-to-r from-indigo-500 to-indigo-600',
        }
      );
    }

    return stats;
  };

  // Données pour les graphiques avec vraies données backend
  const getDetectionTrendData = () => {
    if (!detectionTrends?.daily_detections) {
      return {
        labels: [],
        datasets: [{
          label: 'Détections',
          data: [],
          borderColor: '#3B82F6',
          backgroundColor: 'rgba(59, 130, 246, 0.1)',
          tension: 0.4,
        }],
      };
    }

    const labels = detectionTrends.daily_detections.map(item =>
      new Date(item.day).toLocaleDateString('fr-FR', { day: '2-digit', month: 'short' })
    );
    const data = detectionTrends.daily_detections.map(item => item.count);

    return {
      labels,
      datasets: [{
        label: 'Détections par jour',
        data,
        borderColor: '#3B82F6',
        backgroundColor: 'rgba(59, 130, 246, 0.1)',
        tension: 0.4,
        fill: true,
      }],
    };
  };

  const getAlertsDistributionData = () => {
    if (!dashboardStats?.alerts_by_level) {
      return {
        labels: ['CRITICAL', 'HIGH', 'MEDIUM', 'LOW'],
        datasets: [{
          data: [0, 0, 0, 0],
          backgroundColor: ['#EF4444', '#F97316', '#F59E0B', '#10B981'],
          borderColor: ['#DC2626', '#EA580C', '#D97706', '#059669'],
          borderWidth: 2,
        }],
      };
    }

    const { alerts_by_level } = dashboardStats;
    return {
      labels: ['Critique', 'Élevé', 'Moyen', 'Faible'],
      datasets: [{
        data: [
          alerts_by_level.CRITICAL || 0,
          alerts_by_level.HIGH || 0,
          alerts_by_level.MEDIUM || 0,
          alerts_by_level.LOW || 0,
        ],
        backgroundColor: ['#EF4444', '#F97316', '#F59E0B', '#10B981'],
        borderColor: ['#DC2626', '#EA580C', '#D97706', '#059669'],
        borderWidth: 2,
      }],
    };
  };

  const getFinancialImpactData = () => {
    if (!financialImpact?.breakdown_by_risk_level) {
      return {
        labels: [],
        datasets: [{
          label: 'Impact Financier (FCFA)',
          data: [],
          backgroundColor: ['#3B82F6', '#8B5CF6', '#F59E0B', '#EF4444'],
        }],
      };
    }

    const labels = financialImpact.breakdown_by_risk_level.map(item => item.risk_level);
    const data = financialImpact.breakdown_by_risk_level.map(item => item.total_amount);

    return {
      labels,
      datasets: [{
        label: 'Impact Financier (FCFA)',
        data,
        backgroundColor: ['#3B82F6', '#8B5CF6', '#F59E0B', '#EF4444'],
        borderColor: ['#2563EB', '#7C3AED', '#D97706', '#DC2626'],
        borderWidth: 1,
      }],
    };
  };

  // État de chargement global
  if (isLoadingStats) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Loading />
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* En-tête moderne avec indicateur de refresh */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-gradient-to-r from-blue-600 to-blue-800 rounded-2xl p-8 text-white shadow-2xl"
      >
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold mb-2">
              Dashboard Gold Sentinel
            </h1>
            <p className="text-blue-100 text-lg">
              Bienvenue, <span className="text-yellow-300 font-semibold">{user?.full_name}</span>
            </p>
            <div className="flex items-center mt-2 space-x-4">
              <span className="text-sm text-blue-200">{user?.job_title}</span>
              <span className="text-sm text-blue-200">•</span>
              <span className="text-sm text-blue-200 flex items-center">
                <MapPinIcon className="w-4 h-4 mr-1" />
                {user?.authorized_region}
              </span>
              {dashboardStats?.last_analysis_date && (
                <>
                  <span className="text-sm text-blue-200">•</span>
                  <span className="text-sm text-blue-200 flex items-center">
                    <ClockIcon className="w-4 h-4 mr-1" />
                    Dernière analyse: {new Date(dashboardStats.last_analysis_date).toLocaleDateString('fr-FR')}
                  </span>
                </>
              )}
            </div>
          </div>
          <div className="hidden md:block">
            <div className="w-24 h-24 bg-gradient-to-r from-yellow-400 to-yellow-500 rounded-full flex items-center justify-center shadow-lg">
              <ChartPieIcon className="w-12 h-12 text-blue-900" />
            </div>
          </div>
        </div>

        {/* Indicateur refresh automatique */}
        <div className="mt-4 flex items-center justify-between">
          <div className="flex items-center space-x-2 text-blue-200 text-sm">
            <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
            <span>Données temps réel • Refresh automatique 30s</span>
          </div>
          <button
            onClick={() => refetchStats()}
            className="flex items-center space-x-2 px-3 py-1 bg-blue-500 hover:bg-blue-400 rounded-lg transition-colors text-sm"
          >
            <ArrowTrendingUpIcon className="w-4 h-4" />
            <span>Actualiser</span>
          </button>
        </div>
      </motion.div>

      {/* Statistiques modernes */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {getStats().map((stat, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
          >
            <StatCard
              title={stat.title}
              value={stat.value.toLocaleString()}
              icon={stat.icon}
              color={stat.color}
              trend={stat.trend}
            />
          </motion.div>
        ))}
      </div>

      {/* Carte interactive Bondoukou */}
      {permissions.canViewStats() && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <Card className="p-6">
            <h2 className="text-xl font-semibold text-slate-900 mb-6 flex items-center">
              <MapPinIcon className="w-5 h-5 mr-2 text-green-500" />
              Carte Interactive Bondoukou
            </h2>
            {isLoadingDetections ? (
              <div className="h-96 flex items-center justify-center">
                <Loading />
              </div>
            ) : (
              <div className="h-96 rounded-lg overflow-hidden">
                <BondoukouMap
                  detections={detectionsData?.results || []}
                  center={[8.0402, -2.8000]} // Coordonnées Bondoukou
                  zoom={10}
                />
              </div>
            )}
          </Card>
        </motion.div>
      )}

      {/* Actions rapides selon les permissions */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3 }}
      >
        <Card className="p-6">
          <h2 className="text-xl font-semibold text-slate-900 mb-6 flex items-center">
            <PlayIcon className="w-5 h-5 mr-2 text-blue-500" />
            Actions Rapides
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {permissions.canLaunchAnalysis() && (
              <motion.div
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <Button
                  variant="outline"
                  className="h-auto p-4 flex flex-col items-center space-y-2 w-full"
                  onClick={() => window.location.href = '/analysis'}
                >
                  <BeakerIcon className="w-8 h-8 text-orange-500" />
                  <div className="text-center">
                    <p className="font-medium">Lancer Analyse</p>
                    <p className="text-xs text-slate-500">Analyse satellite GEE</p>
                  </div>
                </Button>
              </motion.div>
            )}

            {permissions.canManageInvestigations() && (
              <motion.div
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <Button
                  variant="outline"
                  className="h-auto p-4 flex flex-col items-center space-y-2 w-full"
                  onClick={() => window.location.href = '/investigations'}
                >
                  <ShieldCheckIcon className="w-8 h-8 text-indigo-500" />
                  <div className="text-center">
                    <p className="font-medium">Investigations</p>
                    <p className="text-xs text-slate-500">Gérer & assigner</p>
                  </div>
                </Button>
              </motion.div>
            )}

            {permissions.canViewStats() && (
              <motion.div
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <Button
                  variant="outline"
                  className="h-auto p-4 flex flex-col items-center space-y-2 w-full"
                  onClick={() => window.location.href = '/stats'}
                >
                  <ChartBarIcon className="w-8 h-8 text-pink-500" />
                  <div className="text-center">
                    <p className="font-medium">Statistiques</p>
                    <p className="text-xs text-slate-500">Rapports détaillés</p>
                  </div>
                </Button>
              </motion.div>
            )}

            {permissions.canViewFinancialRisks() && (
              <motion.div
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <Button
                  variant="outline"
                  className="h-auto p-4 flex flex-col items-center space-y-2 w-full"
                  onClick={() => window.location.href = '/financial-risks'}
                >
                  <BanknotesIcon className="w-8 h-8 text-yellow-500" />
                  <div className="text-center">
                    <p className="font-medium">Risques Financiers</p>
                    <p className="text-xs text-slate-500">Impact économique</p>
                  </div>
                </Button>
              </motion.div>
            )}
          </div>
        </Card>
      </motion.div>

      {/* Graphiques et analyses avec vraies données */}
      {permissions.canViewStats() && (
        <>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.4 }}
            >
              <LineChart
                title="Évolution des Détections (30 derniers jours)"
                data={getDetectionTrendData()}
              />
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.5 }}
            >
              <DoughnutChart
                title="Distribution des Alertes par Criticité"
                data={getAlertsDistributionData()}
              />
            </motion.div>
          </div>

          {/* Graphique impact financier */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6 }}
          >
            <BarChart
              title="Impact Financier par Niveau de Risque"
              data={getFinancialImpactData()}
            />
          </motion.div>
        </>
      )}

      {/* Résumé des zones affectées */}
      {permissions.canViewStats() && dashboardStats?.affected_zones && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.7 }}
        >
          <Card className="p-6">
            <h2 className="text-xl font-semibold text-slate-900 mb-6 flex items-center">
              <MapPinIcon className="w-5 h-5 mr-2 text-purple-500" />
              Zones les Plus Affectées
            </h2>
            <div className="space-y-4">
              {dashboardStats.affected_zones.map((zone, index) => (
                <motion.div
                  key={zone.zone}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.1 * index }}
                  className="flex items-center justify-between p-4 bg-gradient-to-r from-purple-50 to-blue-50 rounded-lg border border-purple-100"
                >
                  <div className="flex items-center space-x-3">
                    <div className="w-3 h-3 bg-purple-500 rounded-full"></div>
                    <span className="font-medium text-slate-900">{zone.zone}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="text-sm text-slate-600">{zone.detections} détections</span>
                    <div className="w-16 bg-purple-200 rounded-full h-2">
                      <div
                        className="bg-purple-500 h-2 rounded-full transition-all duration-500"
                        style={{
                          width: `${Math.min((zone.detections / Math.max(...dashboardStats.affected_zones.map(z => z.detections))) * 100, 100)}%`
                        }}
                      ></div>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </Card>
        </motion.div>
      )}
    </div>
  );
};