import React, { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { motion } from 'framer-motion';
import {
  BeakerIcon,
  MapIcon,
  CogIcon,
  PlayIcon,
  PauseIcon,
  StopIcon,
  EyeIcon,
  DocumentArrowDownIcon,
  ClockIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  XCircleIcon,
} from '@heroicons/react/24/outline';
import { useAuth } from '../contexts/AuthContext';
import { usePermissions } from '../hooks/usePermissions';
import analysisService from '../services/analysis.service';
import type {
  AnalysisJob,
  AnalysisParameters,
  AnalysisZone,
  SatelliteSource,
  SpectralIndex
} from '../services/analysis.service';
import { Card, Button, Loading } from '../components/ui';
import { AnalysisConfigurationForm } from '../components/analysis/AnalysisConfigurationForm';
import { AnalysisJobMonitor } from '../components/analysis/AnalysisJobMonitor';
import { AnalysisResultsViewer } from '../components/analysis/AnalysisResultsViewer';
import { ZoneSelectionMap } from '../components/analysis/ZoneSelectionMap';
import toast from 'react-hot-toast';

// Composant Badge de statut pour les jobs
const JobStatusBadge: React.FC<{ status: string; progress?: number }> = ({ status, progress }) => {
  const getStatusConfig = () => {
    switch (status) {
      case 'PENDING':
        return {
          label: 'En attente',
          className: 'bg-yellow-100 text-yellow-800 border-yellow-200',
          icon: ClockIcon,
        };
      case 'RUNNING':
        return {
          label: `En cours ${progress ? `(${progress}%)` : ''}`,
          className: 'bg-blue-100 text-blue-800 border-blue-200',
          icon: PlayIcon,
        };
      case 'COMPLETED':
        return {
          label: 'Terminée',
          className: 'bg-green-100 text-green-800 border-green-200',
          icon: CheckCircleIcon,
        };
      case 'FAILED':
        return {
          label: 'Échouée',
          className: 'bg-red-100 text-red-800 border-red-200',
          icon: XCircleIcon,
        };
      case 'CANCELLED':
        return {
          label: 'Annulée',
          className: 'bg-gray-100 text-gray-800 border-gray-200',
          icon: StopIcon,
        };
      default:
        return {
          label: status,
          className: 'bg-gray-100 text-gray-800 border-gray-200',
          icon: ExclamationTriangleIcon,
        };
    }
  };

  const config = getStatusConfig();
  const IconComponent = config.icon;

  return (
    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${config.className}`}>
      <IconComponent className="w-3 h-3 mr-1" />
      {config.label}
    </span>
  );
};

export const AnalysisPage: React.FC = () => {
  const { user } = useAuth();
  const permissions = usePermissions();
  const queryClient = useQueryClient();

  // États locaux
  const [activeTab, setActiveTab] = useState<'new' | 'jobs' | 'results'>('new');
  const [selectedJob, setSelectedJob] = useState<AnalysisJob | null>(null);
  const [showConfigForm, setShowConfigForm] = useState(false);
  const [selectedZone, setSelectedZone] = useState<AnalysisZone | null>(null);

  // Vérification des permissions
  if (!permissions.canLaunchAnalysis()) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Card className="p-8 text-center">
          <ExclamationTriangleIcon className="w-12 h-12 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-bold text-gray-900 mb-2">
            Accès non autorisé
          </h2>
          <p className="text-gray-600">
            Vous n'avez pas les permissions nécessaires pour accéder au module d'analyse spectrale.
          </p>
        </Card>
      </div>
    );
  }

  // Récupération des données
  const {
    data: jobsData,
    isLoading: isLoadingJobs,
    refetch: refetchJobs
  } = useQuery({
    queryKey: ['analysis-jobs'],
    queryFn: () => analysisService.getAnalysisJobs(),
    refetchInterval: 5000, // Refresh toutes les 5 secondes pour monitoring temps réel
  });

  const { data: zones } = useQuery({
    queryKey: ['analysis-zones'],
    queryFn: () => analysisService.getAnalysisZones(),
  });

  const { data: satelliteSources } = useQuery({
    queryKey: ['satellite-sources'],
    queryFn: () => analysisService.getSatelliteSources(),
  });

  const { data: spectralIndices } = useQuery({
    queryKey: ['spectral-indices'],
    queryFn: () => analysisService.getSpectralIndices(),
  });

  // Mutation pour lancer une analyse
  const startAnalysisMutation = useMutation({
    mutationFn: ({ parameters, name }: { parameters: AnalysisParameters; name: string }) =>
      analysisService.startAnalysis(parameters, name),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['analysis-jobs'] });
      setShowConfigForm(false);
      setActiveTab('jobs');
      toast.success(`Analyse "${data.name}" lancée avec succès`);
    },
    onError: (error: any) => {
      toast.error(error.message || 'Erreur lors du lancement de l\'analyse');
    }
  });

  // Mutation pour annuler une analyse
  const cancelJobMutation = useMutation({
    mutationFn: (jobId: string) => analysisService.cancelAnalysisJob(jobId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['analysis-jobs'] });
      toast.success('Analyse annulée');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Erreur lors de l\'annulation');
    }
  });

  const handleStartAnalysis = (parameters: AnalysisParameters, name: string) => {
    startAnalysisMutation.mutate({ parameters, name });
  };

  const handleCancelJob = (jobId: string) => {
    if (confirm('Êtes-vous sûr de vouloir annuler cette analyse ?')) {
      cancelJobMutation.mutate(jobId);
    }
  };

  const handleDownloadReport = async (jobId: string) => {
    try {
      const blob = await analysisService.downloadAnalysisReport(jobId);
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `rapport-analyse-${jobId}.pdf`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      toast.success('Rapport téléchargé');
    } catch (error) {
      toast.error('Erreur lors du téléchargement');
    }
  };

  const jobs = jobsData?.results || [];
  const runningJobs = jobs.filter(job => job.status === 'RUNNING');
  const completedJobs = jobs.filter(job => job.status === 'COMPLETED');

  return (
    <div className="space-y-6">
      {/* En-tête */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-gradient-to-r from-purple-600 to-purple-800 rounded-2xl p-6 text-white shadow-xl"
      >
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold mb-2">
              Analyse Spectrale Satellite
            </h1>
            <p className="text-purple-100">
              Détection automatisée d'orpaillage via Google Earth Engine
            </p>
          </div>
          <div className="hidden md:block">
            <div className="w-16 h-16 bg-gradient-to-r from-yellow-400 to-yellow-500 rounded-full flex items-center justify-center">
              <BeakerIcon className="w-8 h-8 text-purple-900" />
            </div>
          </div>
        </div>

        {/* Statistiques rapides */}
        <div className="mt-4 grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-purple-500 bg-opacity-50 rounded-lg p-3">
            <div className="text-sm text-purple-200">Analyses en cours</div>
            <div className="text-xl font-bold">{runningJobs.length}</div>
          </div>
          <div className="bg-purple-500 bg-opacity-50 rounded-lg p-3">
            <div className="text-sm text-purple-200">Terminées ce mois</div>
            <div className="text-xl font-bold">{completedJobs.length}</div>
          </div>
          <div className="bg-purple-500 bg-opacity-50 rounded-lg p-3">
            <div className="text-sm text-purple-200">Zones surveillées</div>
            <div className="text-xl font-bold">{zones?.length || 0}</div>
          </div>
        </div>
      </motion.div>

      {/* Navigation par onglets */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
      >
        <Card className="p-1">
          <div className="flex space-x-1">
            <button
              onClick={() => setActiveTab('new')}
              className={`flex-1 flex items-center justify-center px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                activeTab === 'new'
                  ? 'bg-purple-100 text-purple-700'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
              }`}
            >
              <PlayIcon className="w-4 h-4 mr-2" />
              Nouvelle Analyse
            </button>
            <button
              onClick={() => setActiveTab('jobs')}
              className={`flex-1 flex items-center justify-center px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                activeTab === 'jobs'
                  ? 'bg-purple-100 text-purple-700'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
              }`}
            >
              <ClockIcon className="w-4 h-4 mr-2" />
              Suivi Analyses ({jobs.length})
            </button>
            <button
              onClick={() => setActiveTab('results')}
              className={`flex-1 flex items-center justify-center px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                activeTab === 'results'
                  ? 'bg-purple-100 text-purple-700'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
              }`}
            >
              <EyeIcon className="w-4 h-4 mr-2" />
              Résultats ({completedJobs.length})
            </button>
          </div>
        </Card>
      </motion.div>

      {/* Contenu des onglets */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
      >
        {activeTab === 'new' && (
          <div className="space-y-6">
            {/* Étape 1: Sélection de zone */}
            <Card className="p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                <MapIcon className="w-5 h-5 mr-2 text-purple-500" />
                Étape 1: Sélection de la zone d'analyse
              </h2>

              {!selectedZone ? (
                <div>
                  <p className="text-gray-600 mb-4">
                    Sélectionnez une zone prédéfinie ou dessinez une zone personnalisée sur la carte.
                  </p>
                  <ZoneSelectionMap
                    zones={zones || []}
                    onZoneSelected={setSelectedZone}
                    center={[8.0402, -2.8000]} // Bondoukou
                    zoom={10}
                  />
                </div>
              ) : (
                <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="font-medium text-green-900">{selectedZone.name}</h3>
                      <p className="text-sm text-green-700">
                        Surface: {selectedZone.area_hectares.toFixed(2)} hectares
                      </p>
                      <p className="text-sm text-green-700">
                        Centre: {selectedZone.center_lat.toFixed(4)}, {selectedZone.center_lng.toFixed(4)}
                      </p>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setSelectedZone(null)}
                    >
                      Changer
                    </Button>
                  </div>
                </div>
              )}
            </Card>

            {/* Étape 2: Configuration des paramètres */}
            {selectedZone && (
              <Card className="p-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                  <CogIcon className="w-5 h-5 mr-2 text-purple-500" />
                  Étape 2: Configuration des paramètres
                </h2>

                {!showConfigForm ? (
                  <div className="text-center py-8">
                    <CogIcon className="w-12 h-12 text-gray-300 mx-auto mb-4" />
                    <p className="text-gray-600 mb-4">
                      Configurez les paramètres d'analyse satellite pour la zone sélectionnée.
                    </p>
                    <Button
                      onClick={() => setShowConfigForm(true)}
                      className="flex items-center space-x-2"
                    >
                      <CogIcon className="w-4 h-4" />
                      <span>Configurer l'analyse</span>
                    </Button>
                  </div>
                ) : (
                  <AnalysisConfigurationForm
                    zone={selectedZone}
                    satelliteSources={satelliteSources || []}
                    spectralIndices={spectralIndices || []}
                    onSubmit={handleStartAnalysis}
                    onCancel={() => setShowConfigForm(false)}
                    isLoading={startAnalysisMutation.isPending}
                  />
                )}
              </Card>
            )}
          </div>
        )}

        {activeTab === 'jobs' && (
          <div className="space-y-6">
            {isLoadingJobs ? (
              <div className="flex justify-center py-12">
                <Loading />
              </div>
            ) : jobs.length === 0 ? (
              <Card className="p-12 text-center">
                <BeakerIcon className="w-12 h-12 text-gray-300 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Aucune analyse
                </h3>
                <p className="text-gray-500 mb-4">
                  Vous n'avez pas encore lancé d'analyse spectrale.
                </p>
                <Button
                  onClick={() => setActiveTab('new')}
                  className="flex items-center space-x-2"
                >
                  <PlayIcon className="w-4 h-4" />
                  <span>Lancer une analyse</span>
                </Button>
              </Card>
            ) : (
              <div className="space-y-4">
                {jobs.map((job, index) => (
                  <motion.div
                    key={job.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.05 }}
                  >
                    <Card className="p-6">
                      <div className="flex items-center justify-between mb-4">
                        <div className="flex items-center space-x-3">
                          <div className="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center">
                            <BeakerIcon className="w-5 h-5 text-purple-600" />
                          </div>
                          <div>
                            <h3 className="font-medium text-gray-900">{job.name}</h3>
                            <p className="text-sm text-gray-500">
                              Créée le {new Date(job.created_at).toLocaleDateString('fr-FR')} par {job.created_by_name}
                            </p>
                          </div>
                        </div>
                        <JobStatusBadge status={job.status} progress={job.progress} />
                      </div>

                      {/* Barre de progression pour les jobs en cours */}
                      {job.status === 'RUNNING' && (
                        <div className="mb-4">
                          <div className="flex justify-between text-sm text-gray-600 mb-1">
                            <span>Progression</span>
                            <span>{job.progress}%</span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div
                              className="bg-blue-500 h-2 rounded-full transition-all duration-500"
                              style={{ width: `${job.progress}%` }}
                            ></div>
                          </div>
                        </div>
                      )}

                      {/* Informations sur les paramètres */}
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4 text-sm">
                        <div>
                          <span className="text-gray-600">Type d'analyse :</span>
                          <span className="ml-2 font-medium">{job.parameters.analysis_type}</span>
                        </div>
                        <div>
                          <span className="text-gray-600">Période :</span>
                          <span className="ml-2 font-medium">
                            {new Date(job.parameters.start_date).toLocaleDateString('fr-FR')} - {new Date(job.parameters.end_date).toLocaleDateString('fr-FR')}
                          </span>
                        </div>
                        <div>
                          <span className="text-gray-600">Sensibilité :</span>
                          <span className="ml-2 font-medium">{job.parameters.detection_sensitivity}</span>
                        </div>
                      </div>

                      {/* Actions */}
                      <div className="flex items-center justify-end space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setSelectedJob(job)}
                        >
                          <EyeIcon className="w-4 h-4 mr-1" />
                          Détails
                        </Button>

                        {job.status === 'RUNNING' && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleCancelJob(job.id)}
                            disabled={cancelJobMutation.isPending}
                          >
                            <StopIcon className="w-4 h-4 mr-1" />
                            Annuler
                          </Button>
                        )}

                        {job.status === 'COMPLETED' && job.results_url && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleDownloadReport(job.id)}
                          >
                            <DocumentArrowDownIcon className="w-4 h-4 mr-1" />
                            Rapport
                          </Button>
                        )}
                      </div>
                    </Card>
                  </motion.div>
                ))}
              </div>
            )}
          </div>
        )}

        {activeTab === 'results' && (
          <div className="space-y-6">
            {completedJobs.length === 0 ? (
              <Card className="p-12 text-center">
                <EyeIcon className="w-12 h-12 text-gray-300 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Aucun résultat
                </h3>
                <p className="text-gray-500">
                  Aucune analyse n'a encore été terminée.
                </p>
              </Card>
            ) : (
              <AnalysisResultsViewer
                jobs={completedJobs}
                onViewDetails={setSelectedJob}
                onDownloadReport={handleDownloadReport}
              />
            )}
          </div>
        )}
      </motion.div>

      {/* Modal détails job */}
      {selectedJob && (
        <AnalysisJobMonitor
          job={selectedJob}
          onClose={() => setSelectedJob(null)}
          onCancel={handleCancelJob}
          onDownloadReport={handleDownloadReport}
        />
      )}
    </div>
  );
};
