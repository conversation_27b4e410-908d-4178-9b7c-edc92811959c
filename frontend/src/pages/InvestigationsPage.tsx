import React, { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { motion } from 'framer-motion';
import {
  MagnifyingGlassIcon,
  UserGroupIcon,
  CalendarIcon,
  MapPinIcon,
  EyeIcon,
  PencilIcon,
  UserPlusIcon,
  CheckCircleIcon,
  ClockIcon,
  ExclamationTriangleIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  FunnelIcon,
} from '@heroicons/react/24/outline';
import { useAuth } from '../contexts/AuthContext';
import { usePermissions } from '../hooks/usePermissions';
import investigationService from '../services/investigation.service';
import type { Investigation, Agent } from '../services/investigation.service';
import { Card, Button, Loading, Modal } from '../components/ui';
import { AssignmentModal } from '../components/investigations/AssignmentModal';
import { ResultSubmissionForm } from '../components/investigations/ResultSubmissionForm';
import toast from 'react-hot-toast';

// Composant Badge de statut selon spécifications DoD
const StatusBadge: React.FC<{ status: string }> = ({ status }) => {
  const getStatusConfig = () => {
    switch (status) {
      case 'PENDING':
        return {
          label: 'En attente',
          className: 'bg-orange-100 text-orange-800 border-orange-200',
          icon: ClockIcon,
        };
      case 'ASSIGNED':
        return {
          label: 'Assignée',
          className: 'bg-blue-100 text-blue-800 border-blue-200',
          icon: UserPlusIcon,
        };
      case 'IN_PROGRESS':
        return {
          label: 'En cours',
          className: 'bg-blue-100 text-blue-800 border-blue-200',
          icon: ClockIcon,
        };
      case 'COMPLETED':
        return {
          label: 'Terminée',
          className: 'bg-green-100 text-green-800 border-green-200',
          icon: CheckCircleIcon,
        };
      default:
        return {
          label: status,
          className: 'bg-gray-100 text-gray-800 border-gray-200',
          icon: ExclamationTriangleIcon,
        };
    }
  };

  const config = getStatusConfig();
  const IconComponent = config.icon;

  return (
    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${config.className}`}>
      <IconComponent className="w-3 h-3 mr-1" />
      {config.label}
    </span>
  );
};

export const InvestigationsPage: React.FC = () => {
  const { user } = useAuth();
  const permissions = usePermissions();
  const queryClient = useQueryClient();

  // États locaux
  const [selectedInvestigation, setSelectedInvestigation] = useState<Investigation | null>(null);
  const [showAssignmentModal, setShowAssignmentModal] = useState(false);
  const [showResultForm, setShowResultForm] = useState(false);
  const [currentPage, setCurrentPage] = useState(0);
  const [pageSize] = useState(20);
  const [filters, setFilters] = useState({
    status: '',
    agent: '',
    region: '',
  });

  // Déterminer si l'utilisateur est un agent terrain
  const isAgentTerrain = permissions.isAgentTerrain();

  // Récupération des investigations selon le rôle
  const {
    data: investigationsData,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['investigations', filters, currentPage, isAgentTerrain],
    queryFn: async () => {
      if (isAgentTerrain) {
        // Agent terrain : seulement ses investigations
        return await investigationService.getMyInvestigations();
      } else {
        // Responsable : toutes les investigations avec filtres
        return await investigationService.getFilteredInvestigations(
          filters,
          currentPage,
          pageSize
        );
      }
    },
    refetchInterval: 30000, // Refresh toutes les 30 secondes
  });

  // Récupération des agents disponibles (pour Responsables uniquement)
  const { data: agentsData } = useQuery({
    queryKey: ['available-agents'],
    queryFn: () => investigationService.getAvailableAgents(),
    enabled: !isAgentTerrain,
    refetchInterval: 60000, // Refresh toutes les minutes
  });

  // Mutation pour assigner une investigation
  const assignInvestigationMutation = useMutation({
    mutationFn: ({ investigationId, agentId, priority, notes }: {
      investigationId: number;
      agentId: number;
      priority: string;
      notes: string;
    }) => investigationService.assignInvestigation(investigationId, agentId, priority, notes),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['investigations'] });
      queryClient.invalidateQueries({ queryKey: ['available-agents'] });
      setShowAssignmentModal(false);
      setSelectedInvestigation(null);
      toast.success(`Investigation assignée à ${data.agent_info.name}`);
      if (data.warning) {
        toast.warning(data.warning);
      }
    },
    onError: (error: any) => {
      toast.error(error.message || 'Erreur lors de l\'assignation');
    }
  });

  // Mutation pour soumettre un résultat
  const submitResultMutation = useMutation({
    mutationFn: ({ investigationId, result, fieldNotes, investigationDate }: {
      investigationId: number;
      result: string;
      fieldNotes: string;
      investigationDate?: string;
    }) => investigationService.submitResult(investigationId, result, fieldNotes, investigationDate),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['investigations'] });
      setShowResultForm(false);
      setSelectedInvestigation(null);
      toast.success('Résultat soumis avec succès');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Erreur lors de la soumission');
    }
  });

  // Gestionnaires d'événements
  const handleAssignInvestigation = (investigation: Investigation) => {
    setSelectedInvestigation(investigation);
    setShowAssignmentModal(true);
  };

  const handleSubmitResult = (investigation: Investigation) => {
    setSelectedInvestigation(investigation);
    setShowResultForm(true);
  };

  const handleAssignmentSubmit = (agentId: number, priority: string, notes: string) => {
    if (selectedInvestigation) {
      assignInvestigationMutation.mutate({
        investigationId: selectedInvestigation.id,
        agentId,
        priority,
        notes,
      });
    }
  };

  const handleResultSubmit = (result: string, fieldNotes: string, investigationDate?: string) => {
    if (selectedInvestigation) {
      submitResultMutation.mutate({
        investigationId: selectedInvestigation.id,
        result,
        fieldNotes,
        investigationDate,
      });
    }
  };

  const handleFilterChange = (key: string, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }));
    setCurrentPage(0); // Reset à la première page
  };

  // Données pour l'affichage
  const investigations = investigationsData?.results || [];
  const totalCount = investigationsData?.count || 0;
  const totalPages = Math.ceil(totalCount / pageSize);

  // Gestion des erreurs
  useEffect(() => {
    if (error) {
      toast.error('Erreur lors du chargement des investigations');
      console.error('Erreur investigations:', error);
    }
  }, [error]);

  // État de chargement
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Loading />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* En-tête */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-gradient-to-r from-indigo-600 to-indigo-800 rounded-2xl p-6 text-white shadow-xl"
      >
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold mb-2">
              {isAgentTerrain ? 'Mes Investigations' : 'Gestion des Investigations'}
            </h1>
            <p className="text-indigo-100">
              {isAgentTerrain
                ? 'Investigations qui vous sont assignées'
                : 'Assignation et suivi des investigations terrain'
              }
            </p>
          </div>
          <div className="hidden md:block">
            <div className="w-16 h-16 bg-gradient-to-r from-yellow-400 to-yellow-500 rounded-full flex items-center justify-center">
              <MagnifyingGlassIcon className="w-8 h-8 text-indigo-900" />
            </div>
          </div>
        </div>

        {/* Statistiques rapides */}
        <div className="mt-4 grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-indigo-500 bg-opacity-50 rounded-lg p-3">
            <div className="text-sm text-indigo-200">Total</div>
            <div className="text-xl font-bold">{totalCount}</div>
          </div>
          <div className="bg-indigo-500 bg-opacity-50 rounded-lg p-3">
            <div className="text-sm text-indigo-200">En attente</div>
            <div className="text-xl font-bold">
              {investigations.filter(inv => inv.status === 'PENDING').length}
            </div>
          </div>
          <div className="bg-indigo-500 bg-opacity-50 rounded-lg p-3">
            <div className="text-sm text-indigo-200">Terminées</div>
            <div className="text-xl font-bold">
              {investigations.filter(inv => inv.status === 'COMPLETED').length}
            </div>
          </div>
        </div>
      </motion.div>

      {/* Filtres (seulement pour Responsables) */}
      {!isAgentTerrain && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
        >
          <Card className="p-4">
            <div className="flex items-center mb-4">
              <FunnelIcon className="w-5 h-5 mr-2 text-gray-500" />
              <h3 className="text-lg font-semibold text-gray-900">Filtres</h3>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Statut
                </label>
                <select
                  value={filters.status}
                  onChange={(e) => handleFilterChange('status', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                >
                  <option value="">Tous les statuts</option>
                  <option value="PENDING">En attente</option>
                  <option value="ASSIGNED">Assignée</option>
                  <option value="IN_PROGRESS">En cours</option>
                  <option value="COMPLETED">Terminée</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Agent
                </label>
                <select
                  value={filters.agent}
                  onChange={(e) => handleFilterChange('agent', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                >
                  <option value="">Tous les agents</option>
                  {agentsData?.agents.map((agent) => (
                    <option key={agent.id} value={agent.id.toString()}>
                      {agent.full_name}
                    </option>
                  ))}
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Région
                </label>
                <select
                  value={filters.region}
                  onChange={(e) => handleFilterChange('region', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                >
                  <option value="">Toutes les régions</option>
                  <option value="Bondoukou">Bondoukou</option>
                </select>
              </div>
            </div>
          </Card>
        </motion.div>
      )}

      {/* Table des investigations */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
      >
        <Card className="overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">
              Investigations ({totalCount})
            </h3>
          </div>

          {investigations.length === 0 ? (
            <div className="text-center py-12">
              <MagnifyingGlassIcon className="w-12 h-12 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Aucune investigation
              </h3>
              <p className="text-gray-500">
                {isAgentTerrain
                  ? 'Aucune investigation ne vous est assignée pour le moment.'
                  : 'Aucune investigation ne correspond aux filtres sélectionnés.'
                }
              </p>
            </div>
          ) : (
            <>
              {/* Table responsive */}
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Investigation
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Statut
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Agent assigné
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Date création
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {investigations.map((investigation, index) => (
                      <motion.tr
                        key={investigation.id}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: index * 0.05 }}
                        className="hover:bg-gray-50 transition-colors"
                      >
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <div className="flex-shrink-0 w-10 h-10 bg-indigo-100 rounded-full flex items-center justify-center">
                              <span className="text-sm font-medium text-indigo-600">
                                #{investigation.id}
                              </span>
                            </div>
                            <div className="ml-4">
                              <div className="text-sm font-medium text-gray-900">
                                {investigation.detection_info?.type || 'Type non spécifié'}
                              </div>
                              <div className="text-sm text-gray-500">
                                {investigation.detection_info?.area_hectares?.toFixed(2)} ha
                                {investigation.detection_info?.confidence_score && (
                                  <span className="ml-2">
                                    • {Math.round(investigation.detection_info.confidence_score * 100)}% confiance
                                  </span>
                                )}
                              </div>
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <StatusBadge status={investigation.status} />
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          {investigation.assigned_to_name ? (
                            <div className="flex items-center">
                              <div className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center">
                                <span className="text-xs font-medium text-gray-700">
                                  {investigation.assigned_to_name.split(' ').map(n => n[0]).join('')}
                                </span>
                              </div>
                              <span className="ml-2 text-sm text-gray-900">
                                {investigation.assigned_to_name}
                              </span>
                            </div>
                          ) : (
                            <span className="text-sm text-gray-500">Non assignée</span>
                          )}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          <div className="flex items-center">
                            <CalendarIcon className="w-4 h-4 mr-1" />
                            {new Date(investigation.created_at).toLocaleDateString('fr-FR')}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <div className="flex items-center space-x-2">
                            <button
                              onClick={() => setSelectedInvestigation(investigation)}
                              className="text-indigo-600 hover:text-indigo-900 p-1 rounded-full hover:bg-indigo-100 transition-colors"
                              title="Voir détails"
                            >
                              <EyeIcon className="w-4 h-4" />
                            </button>

                            {/* Actions selon le rôle et le statut */}
                            {!isAgentTerrain && investigation.status === 'PENDING' && (
                              <button
                                onClick={() => handleAssignInvestigation(investigation)}
                                className="text-blue-600 hover:text-blue-900 p-1 rounded-full hover:bg-blue-100 transition-colors"
                                title="Assigner agent"
                              >
                                <UserPlusIcon className="w-4 h-4" />
                              </button>
                            )}

                            {isAgentTerrain &&
                             (investigation.status === 'ASSIGNED' || investigation.status === 'IN_PROGRESS') &&
                             investigation.assigned_to === user?.id && (
                              <button
                                onClick={() => handleSubmitResult(investigation)}
                                className="text-green-600 hover:text-green-900 p-1 rounded-full hover:bg-green-100 transition-colors"
                                title="Soumettre résultat"
                              >
                                <CheckCircleIcon className="w-4 h-4" />
                              </button>
                            )}
                          </div>
                        </td>
                      </motion.tr>
                    ))}
                  </tbody>
                </table>
              </div>

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="px-6 py-4 border-t border-gray-200 flex items-center justify-between">
                  <div className="text-sm text-gray-700">
                    Page {currentPage + 1} sur {totalPages} • {totalCount} investigation{totalCount > 1 ? 's' : ''}
                  </div>
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => setCurrentPage(prev => Math.max(0, prev - 1))}
                      disabled={currentPage === 0}
                      className="p-2 rounded-lg border border-gray-300 disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50 transition-colors"
                    >
                      <ChevronLeftIcon className="w-4 h-4" />
                    </button>
                    <button
                      onClick={() => setCurrentPage(prev => Math.min(totalPages - 1, prev + 1))}
                      disabled={currentPage === totalPages - 1}
                      className="p-2 rounded-lg border border-gray-300 disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50 transition-colors"
                    >
                      <ChevronRightIcon className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              )}
            </>
          )}
        </Card>
      </motion.div>

      {/* Modal assignation agent */}
      <AssignmentModal
        isOpen={showAssignmentModal}
        onClose={() => setShowAssignmentModal(false)}
        investigation={selectedInvestigation}
        agents={agentsData?.agents || []}
        onAssign={handleAssignmentSubmit}
        isLoading={assignInvestigationMutation.isPending}
      />

      {/* Formulaire soumission résultat */}
      <ResultSubmissionForm
        isOpen={showResultForm}
        onClose={() => setShowResultForm(false)}
        investigation={selectedInvestigation}
        onSubmit={handleResultSubmit}
        isLoading={submitResultMutation.isPending}
      />

      {/* Modal détails investigation */}
      {selectedInvestigation && !showAssignmentModal && !showResultForm && (
        <Modal
          isOpen={!!selectedInvestigation}
          onClose={() => setSelectedInvestigation(null)}
          size="lg"
        >
          <div className="p-6">
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-indigo-100 rounded-full flex items-center justify-center">
                  <span className="text-sm font-medium text-indigo-600">
                    #{selectedInvestigation.id}
                  </span>
                </div>
                <div>
                  <h2 className="text-xl font-bold text-gray-900">
                    Détails Investigation #{selectedInvestigation.id}
                  </h2>
                  <p className="text-sm text-gray-600">
                    {selectedInvestigation.detection_info?.type}
                  </p>
                </div>
              </div>
              <StatusBadge status={selectedInvestigation.status} />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div>
                  <h3 className="text-sm font-medium text-gray-700 mb-1">Informations détection</h3>
                  <div className="bg-gray-50 rounded-lg p-3 space-y-2">
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">Type :</span>
                      <span className="font-medium">{selectedInvestigation.detection_info?.type}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">Surface :</span>
                      <span className="font-medium">{selectedInvestigation.detection_info?.area_hectares?.toFixed(2)} ha</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">Confiance :</span>
                      <span className="font-medium">
                        {Math.round((selectedInvestigation.detection_info?.confidence_score || 0) * 100)}%
                      </span>
                    </div>
                  </div>
                </div>

                <div>
                  <h3 className="text-sm font-medium text-gray-700 mb-1">Coordonnées</h3>
                  <div className="bg-gray-50 rounded-lg p-3">
                    <div className="flex items-center text-sm">
                      <MapPinIcon className="w-4 h-4 mr-2 text-gray-500" />
                      <span className="font-mono">{selectedInvestigation.target_coordinates}</span>
                    </div>
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <div>
                  <h3 className="text-sm font-medium text-gray-700 mb-1">Assignation</h3>
                  <div className="bg-gray-50 rounded-lg p-3">
                    {selectedInvestigation.assigned_to_name ? (
                      <div className="flex items-center">
                        <div className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center">
                          <span className="text-xs font-medium text-gray-700">
                            {selectedInvestigation.assigned_to_name.split(' ').map(n => n[0]).join('')}
                          </span>
                        </div>
                        <span className="ml-2 text-sm font-medium">
                          {selectedInvestigation.assigned_to_name}
                        </span>
                      </div>
                    ) : (
                      <span className="text-sm text-gray-500">Non assignée</span>
                    )}
                  </div>
                </div>

                <div>
                  <h3 className="text-sm font-medium text-gray-700 mb-1">Dates</h3>
                  <div className="bg-gray-50 rounded-lg p-3 space-y-2">
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">Création :</span>
                      <span className="font-medium">
                        {new Date(selectedInvestigation.created_at).toLocaleDateString('fr-FR')}
                      </span>
                    </div>
                    {selectedInvestigation.investigation_date && (
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600">Investigation :</span>
                        <span className="font-medium">
                          {new Date(selectedInvestigation.investigation_date).toLocaleDateString('fr-FR')}
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>

            {selectedInvestigation.access_instructions && (
              <div className="mt-6">
                <h3 className="text-sm font-medium text-gray-700 mb-2">Instructions d'accès</h3>
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                  <p className="text-sm text-blue-800">{selectedInvestigation.access_instructions}</p>
                </div>
              </div>
            )}

            {selectedInvestigation.field_notes && (
              <div className="mt-6">
                <h3 className="text-sm font-medium text-gray-700 mb-2">Notes de terrain</h3>
                <div className="bg-green-50 border border-green-200 rounded-lg p-3">
                  <p className="text-sm text-green-800 whitespace-pre-wrap">{selectedInvestigation.field_notes}</p>
                </div>
              </div>
            )}

            <div className="flex justify-end mt-6 pt-4 border-t border-gray-200">
              <Button
                onClick={() => setSelectedInvestigation(null)}
                variant="outline"
              >
                Fermer
              </Button>
            </div>
          </div>
        </Modal>
      )}
    </div>
  );
};