import React, { useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { motion } from 'framer-motion';
import {
  ShieldCheckIcon,
  EnvelopeIcon,
  LockClosedIcon,
  EyeIcon,
  EyeSlashIcon,
  ExclamationCircleIcon,
  UserGroupIcon,
  ChevronDownIcon,
  ChevronUpIcon
} from '@heroicons/react/24/outline';
import { useAuth } from '../contexts/AuthContext';
import { Button, Loading } from '../components/ui';
import toast from 'react-hot-toast';

// Comptes de test pour le développement
const TEST_ACCOUNTS = [
  {
    role: 'Administrateur',
    email: '<EMAIL>',
    password: 'admin123',
    icon: '👑',
    description: 'Accès complet au système'
  },
  {
    role: 'Responsable Régional',
    email: '<EMAIL>',
    password: 'password123',
    icon: '👔',
    description: 'Gestion régionale et statistiques'
  },
  {
    role: 'Agent <PERSON><PERSON><PERSON>',
    email: '<EMAIL>',
    password: 'password123',
    icon: '🔍',
    description: 'Analyse et validation des détections'
  },
  {
    role: 'Agent Technique',
    email: '<EMAIL>',
    password: 'password123',
    icon: '🛠️',
    description: 'Support technique et logs'
  },
  {
    role: 'Agent Terrain',
    email: '<EMAIL>',
    password: 'password123',
    icon: '🚶',
    description: 'Investigations terrain'
  }
];

export const LoginPage: React.FC = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const [showTestAccounts, setShowTestAccounts] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();
  const { login } = useAuth();

  const from = location.state?.from?.pathname || '/';

  const handleTestAccountClick = (account: typeof TEST_ACCOUNTS[0]) => {
    setEmail(account.email);
    setPassword(account.password);
    setError('');
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setLoading(true);

    // Validation côté client
    if (!email || !password) {
      setError('Veuillez remplir tous les champs');
      setLoading(false);
      return;
    }

    if (!email.includes('@')) {
      setError('Veuillez entrer une adresse email valide');
      setLoading(false);
      return;
    }

    try {
      await login(email, password);
      // La redirection est gérée automatiquement dans AuthContext
    } catch (err: any) {
      const errorMessage = err.message || 'Identifiants invalides';
      setError(errorMessage);
      console.error('Erreur login:', err);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-night-blue via-slate-800 to-slate-900 flex items-center justify-center p-4">
      {/* Arrière-plan animé */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-gold/10 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-sky-blue/10 rounded-full blur-3xl animate-pulse"></div>
      </div>

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="relative w-full max-w-md"
      >
        {/* Logo et titre */}
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: 0.2, duration: 0.5 }}
          className="text-center mb-8"
        >
          <div className="w-20 h-20 bg-gradient-gold rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-glow-gold">
            <ShieldCheckIcon className="w-10 h-10 text-night-blue" />
          </div>
          <h1 className="text-3xl font-bold text-white mb-2">GoldSentinel</h1>
          <p className="text-slate-300">Surveillance Minière Intelligente</p>
        </motion.div>

        {/* Formulaire de connexion */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4, duration: 0.5 }}
          className="bg-white/10 backdrop-blur-lg rounded-2xl p-8 shadow-2xl border border-white/20"
        >
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Champ email */}
            <div>
              <label className="block text-sm font-medium text-slate-200 mb-2">
                Adresse email
              </label>
              <div className="relative">
                <EnvelopeIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-slate-400" />
                <input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                  className="w-full pl-10 pr-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-gold focus:border-transparent transition-all"
                  placeholder="<EMAIL>"
                  autoComplete="email"
                />
              </div>
            </div>

            {/* Champ mot de passe */}
            <div>
              <label className="block text-sm font-medium text-slate-200 mb-2">
                Mot de passe
              </label>
              <div className="relative">
                <LockClosedIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-slate-400" />
                <input
                  type={showPassword ? 'text' : 'password'}
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required
                  className="w-full pl-10 pr-12 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-gold focus:border-transparent transition-all"
                  placeholder="••••••••"
                  autoComplete="current-password"
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-slate-400 hover:text-white transition-colors"
                >
                  {showPassword ? <EyeSlashIcon className="w-5 h-5" /> : <EyeIcon className="w-5 h-5" />}
                </button>
              </div>
            </div>

            {/* Message d'erreur */}
            {error && (
              <motion.div
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-red-500/20 border border-red-500/30 rounded-lg p-3 flex items-center space-x-2"
              >
                <ExclamationCircleIcon className="w-5 h-5 text-red-400" />
                <span className="text-red-200 text-sm">{error}</span>
              </motion.div>
            )}

            {/* Bouton de connexion */}
            <Button
              type="submit"
              variant="secondary"
              size="lg"
              loading={loading}
              className="w-full"
            >
              Se connecter
            </Button>
          </form>

          {/* Informations supplémentaires */}
          <div className="mt-6 text-center">
            <p className="text-xs text-slate-400">
              Accès réservé aux agents gouvernementaux autorisés
            </p>
          </div>
        </motion.div>

        {/* Section comptes de test (dev only) */}
        {import.meta.env.DEV && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6, duration: 0.5 }}
            className="mt-6"
          >
            <div className="bg-white/5 backdrop-blur-lg rounded-xl p-4 border border-white/10">
              <button
                type="button"
                onClick={() => setShowTestAccounts(!showTestAccounts)}
                className="w-full flex items-center justify-between text-slate-300 hover:text-white transition-colors"
              >
                <div className="flex items-center space-x-2">
                  <UserGroupIcon className="w-4 h-4" />
                  <span className="text-sm font-medium">Comptes de test</span>
                  <span className="text-xs bg-orange-500/20 text-orange-300 px-2 py-1 rounded-full">
                    DEV
                  </span>
                </div>
                {showTestAccounts ? (
                  <ChevronUpIcon className="w-4 h-4" />
                ) : (
                  <ChevronDownIcon className="w-4 h-4" />
                )}
              </button>

              {showTestAccounts && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  exit={{ opacity: 0, height: 0 }}
                  className="mt-4 space-y-2"
                >
                  {TEST_ACCOUNTS.map((account, index) => (
                    <motion.button
                      key={account.email}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: index * 0.1 }}
                      onClick={() => handleTestAccountClick(account)}
                      className="w-full text-left p-3 bg-white/5 hover:bg-white/10 rounded-lg border border-white/10 hover:border-white/20 transition-all group"
                    >
                      <div className="flex items-center space-x-3">
                        <span className="text-lg">{account.icon}</span>
                        <div className="flex-1">
                          <div className="flex items-center justify-between">
                            <span className="text-sm font-medium text-white group-hover:text-gold transition-colors">
                              {account.role}
                            </span>
                            <span className="text-xs text-slate-400">
                              Cliquer pour remplir
                            </span>
                          </div>
                          <p className="text-xs text-slate-400 mt-1">
                            {account.description}
                          </p>
                          <p className="text-xs text-slate-500 mt-1 font-mono">
                            {account.email}
                          </p>
                        </div>
                      </div>
                    </motion.button>
                  ))}

                  <div className="mt-3 pt-3 border-t border-white/10">
                    <p className="text-xs text-slate-400 text-center">
                      💡 Cliquez sur un compte pour remplir automatiquement les champs
                    </p>
                  </div>
                </motion.div>
              )}
            </div>
          </motion.div>
        )}
      </motion.div>
    </div>
  );
};