import React, { useState } from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { motion } from 'framer-motion';
import {
  UserIcon,
  EnvelopeIcon,
  BriefcaseIcon,
  MapPinIcon,
  ShieldCheckIcon,
  PencilIcon,
  CheckIcon,
  XMarkIcon,
  CameraIcon,
  Cog6ToothIcon,
  BuildingOfficeIcon,
  KeyIcon,
} from '@heroicons/react/24/outline';
import { useAuth } from '../contexts/AuthContext';
import { authService } from '../services/auth.service';
import type { UpdateProfileRequest, ChangePasswordRequest } from '../services/auth.service';
import { Avatar, Card, Button, Loading } from '../components/ui';
import { usePermissions } from '../hooks/usePermissions';
import toast from 'react-hot-toast';

export const AccountPage: React.FC = () => {
  const { user } = useAuth();
  const permissions = usePermissions();
  const queryClient = useQueryClient();

  const [isEditingProfile, setIsEditingProfile] = useState(false);
  const [isChangingPassword, setIsChangingPassword] = useState(false);

  // États pour les formulaires
  const [profileData, setProfileData] = useState({
    first_name: user?.first_name || '',
    last_name: user?.last_name || '',
    job_title: user?.job_title || '',
    institution: user?.institution || '',
  });

  const [passwordData, setPasswordData] = useState({
    old_password: '',
    new_password: '',
    confirm_password: '',
  });

  // Mutations
  const updateProfileMutation = useMutation({
    mutationFn: (data: UpdateProfileRequest) => authService.updateProfile(data),
    onSuccess: () => {
      toast.success('Profil mis à jour avec succès');
      setIsEditingProfile(false);
      queryClient.invalidateQueries({ queryKey: ['user-profile'] });
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });

  const changePasswordMutation = useMutation({
    mutationFn: (data: ChangePasswordRequest) => authService.changePassword(data),
    onSuccess: () => {
      toast.success('Mot de passe modifié avec succès');
      setIsChangingPassword(false);
      setPasswordData({ old_password: '', new_password: '', confirm_password: '' });
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });

  const handleProfileSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    updateProfileMutation.mutate(profileData);
  };

  const handlePasswordSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (passwordData.new_password !== passwordData.confirm_password) {
      toast.error('Les mots de passe ne correspondent pas');
      return;
    }

    if (passwordData.new_password.length < 8) {
      toast.error('Le mot de passe doit contenir au moins 8 caractères');
      return;
    }

    changePasswordMutation.mutate({
      old_password: passwordData.old_password,
      new_password: passwordData.new_password,
    });
  };

  const handleCancelEdit = () => {
    setIsEditingProfile(false);
    setProfileData({
      first_name: user?.first_name || '',
      last_name: user?.last_name || '',
      job_title: user?.job_title || '',
      institution: user?.institution || '',
    });
  };

  if (!user) {
    return <Loading />;
  }

  return (
    <div className="max-w-4xl mx-auto space-y-8">
      {/* En-tête */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="text-center"
      >
        <h1 className="text-3xl font-bold text-slate-900 mb-2">Mon Profil</h1>
        <p className="text-slate-600">Gérez vos informations personnelles et paramètres de sécurité</p>
      </motion.div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Informations principales */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.1 }}
          className="lg:col-span-2"
        >
          <Card className="p-6">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-semibold text-slate-900 flex items-center">
                <UserIcon className="w-5 h-5 mr-2 text-blue-500" />
                Informations personnelles
              </h2>
              {!isEditingProfile && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setIsEditingProfile(true)}
                  className="flex items-center"
                >
                  <PencilIcon className="w-4 h-4 mr-1" />
                  Modifier
                </Button>
              )}
            </div>

            {isEditingProfile ? (
              <form onSubmit={handleProfileSubmit} className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-slate-700 mb-1">
                      Prénom
                    </label>
                    <input
                      type="text"
                      value={profileData.first_name}
                      onChange={(e) => setProfileData({ ...profileData, first_name: e.target.value })}
                      className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-slate-700 mb-1">
                      Nom
                    </label>
                    <input
                      type="text"
                      value={profileData.last_name}
                      onChange={(e) => setProfileData({ ...profileData, last_name: e.target.value })}
                      className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      required
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-1">
                    Fonction
                  </label>
                  <input
                    type="text"
                    value={profileData.job_title}
                    onChange={(e) => setProfileData({ ...profileData, job_title: e.target.value })}
                    className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-1">
                    Institution
                  </label>
                  <input
                    type="text"
                    value={profileData.institution}
                    onChange={(e) => setProfileData({ ...profileData, institution: e.target.value })}
                    className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>

                <div className="flex space-x-3 pt-4">
                  <Button
                    type="submit"
                    loading={updateProfileMutation.isPending}
                    className="flex items-center"
                  >
                    <CheckIcon className="w-4 h-4 mr-1" />
                    Enregistrer
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={handleCancelEdit}
                    className="flex items-center"
                  >
                    <XMarkIcon className="w-4 h-4 mr-1" />
                    Annuler
                  </Button>
                </div>
              </form>
            ) : (
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="flex items-center space-x-3">
                    <UserIcon className="w-5 h-5 text-slate-400" />
                    <div>
                      <p className="text-sm text-slate-500">Nom complet</p>
                      <p className="font-medium text-slate-900">{user.full_name}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <EnvelopeIcon className="w-5 h-5 text-slate-400" />
                    <div>
                      <p className="text-sm text-slate-500">Email</p>
                      <p className="font-medium text-slate-900">{user.email}</p>
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="flex items-center space-x-3">
                    <BriefcaseIcon className="w-5 h-5 text-slate-400" />
                    <div>
                      <p className="text-sm text-slate-500">Fonction</p>
                      <p className="font-medium text-slate-900">{user.job_title || 'Non renseigné'}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <BuildingOfficeIcon className="w-5 h-5 text-slate-400" />
                    <div>
                      <p className="text-sm text-slate-500">Institution</p>
                      <p className="font-medium text-slate-900">{user.institution || 'Non renseigné'}</p>
                    </div>
                  </div>
                </div>

                <div className="flex items-center space-x-3">
                  <MapPinIcon className="w-5 h-5 text-slate-400" />
                  <div>
                    <p className="text-sm text-slate-500">Région autorisée</p>
                    <p className="font-medium text-slate-900">{user.authorized_region}</p>
                  </div>
                </div>
              </div>
            )}
          </Card>
        </motion.div>

        {/* Sidebar avec avatar et autorités */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.2 }}
          className="space-y-6"
        >
          {/* Avatar et infos de base */}
          <Card className="p-6 text-center">
            <Avatar
              fallback={user.full_name}
              size="xl"
              className="mx-auto mb-4"
            />
            <h3 className="text-lg font-semibold text-slate-900">{user.full_name}</h3>
            <p className="text-slate-600">{user.job_title}</p>
            <p className="text-sm text-slate-500 mt-1">{user.email}</p>
          </Card>

          {/* Autorités */}
          <Card className="p-6">
            <h3 className="text-lg font-semibold text-slate-900 mb-4 flex items-center">
              <ShieldCheckIcon className="w-5 h-5 mr-2 text-green-500" />
              Autorités
            </h3>
            <div className="space-y-2">
              {user.authorities.map((authority, index) => (
                <div
                  key={index}
                  className={`flex items-center justify-between p-2 rounded-lg ${
                    authority.name === user.primary_authority
                      ? 'bg-blue-50 border border-blue-200'
                      : 'bg-slate-50'
                  }`}
                >
                  <span className="text-sm font-medium text-slate-900">
                    {authority.name}
                  </span>
                  {authority.name === user.primary_authority && (
                    <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
                      Principal
                    </span>
                  )}
                </div>
              ))}
            </div>
          </Card>
        </motion.div>
      </div>

      {/* Section changement de mot de passe */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3 }}
      >
        <Card className="p-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-semibold text-slate-900 flex items-center">
              <KeyIcon className="w-5 h-5 mr-2 text-red-500" />
              Sécurité
            </h2>
            {!isChangingPassword && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsChangingPassword(true)}
                className="flex items-center"
              >
                <PencilIcon className="w-4 h-4 mr-1" />
                Changer le mot de passe
              </Button>
            )}
          </div>

          {isChangingPassword ? (
            <form onSubmit={handlePasswordSubmit} className="space-y-4 max-w-md">
              <div>
                <label className="block text-sm font-medium text-slate-700 mb-1">
                  Mot de passe actuel
                </label>
                <input
                  type="password"
                  value={passwordData.old_password}
                  onChange={(e) => setPasswordData({ ...passwordData, old_password: e.target.value })}
                  className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-slate-700 mb-1">
                  Nouveau mot de passe
                </label>
                <input
                  type="password"
                  value={passwordData.new_password}
                  onChange={(e) => setPasswordData({ ...passwordData, new_password: e.target.value })}
                  className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  required
                  minLength={8}
                />
                <p className="text-xs text-slate-500 mt-1">Minimum 8 caractères</p>
              </div>

              <div>
                <label className="block text-sm font-medium text-slate-700 mb-1">
                  Confirmer le nouveau mot de passe
                </label>
                <input
                  type="password"
                  value={passwordData.confirm_password}
                  onChange={(e) => setPasswordData({ ...passwordData, confirm_password: e.target.value })}
                  className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  required
                />
              </div>

              <div className="flex space-x-3 pt-4">
                <Button
                  type="submit"
                  loading={changePasswordMutation.isPending}
                  className="flex items-center"
                >
                  <CheckIcon className="w-4 h-4 mr-1" />
                  Modifier
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => {
                    setIsChangingPassword(false);
                    setPasswordData({ old_password: '', new_password: '', confirm_password: '' });
                  }}
                  className="flex items-center"
                >
                  <XMarkIcon className="w-4 h-4 mr-1" />
                  Annuler
                </Button>
              </div>
            </form>
          ) : (
            <div className="text-slate-600">
              <p>Dernière connexion : {user.last_login ? new Date(user.last_login).toLocaleDateString('fr-FR') : 'Jamais'}</p>
              <p className="text-sm text-slate-500 mt-1">
                Pour votre sécurité, changez régulièrement votre mot de passe.
              </p>
            </div>
          )}
        </Card>
      </motion.div>
    </div>
  );
};