import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  BeakerIcon,
  MapIcon,
  ChartBarIcon,
  PlayIcon,
  ClockIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  EyeIcon,
  DocumentArrowDownIcon,
  GlobeAltIcon,
} from '@heroicons/react/24/outline';
import { Card, Button, StatCard, Loading, Badge } from '../components/ui';
import { GEEMap } from '../components/gee/GEEMap';
import { usePermissions } from '../hooks/usePermissions';
import geeService, { AnalysisRequest } from '../services/gee.service';
import detectionService from '../services/detection.service';
import toast from 'react-hot-toast';

export const GEEAnalysisPage: React.FC = () => {
  const permissions = usePermissions();
  const queryClient = useQueryClient();
  
  const [selectedDetection, setSelectedDetection] = useState<any>(null);
  const [analysisResults, setAnalysisResults] = useState<any>(null);
  const [selectedBounds, setSelectedBounds] = useState<google.maps.LatLngBounds | null>(null);

  // Récupérer les détections existantes
  const { data: detectionsData, isLoading: detectionsLoading } = useQuery({
    queryKey: ['detections'],
    queryFn: () => detectionService.getDetections({}),
    refetchInterval: 30000,
  });

  // Récupérer les images disponibles
  const { data: imagesData, isLoading: imagesLoading } = useQuery({
    queryKey: ['gee-images'],
    queryFn: () => geeService.getAvailableImages(),
  });

  // Mutation pour lancer l'analyse
  const analysisMutation = useMutation({
    mutationFn: (request: AnalysisRequest) => geeService.runAnalysis(request),
    onSuccess: (result) => {
      setAnalysisResults(result);
      toast.success(result.message);
      queryClient.invalidateQueries({ queryKey: ['detections'] });
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });

  const handleAnalysisLaunch = (bounds: google.maps.LatLngBounds) => {
    if (!permissions.canLaunchAnalysis()) {
      toast.error('Vous n\'avez pas les permissions pour lancer une analyse');
      return;
    }

    const request: AnalysisRequest = {
      months_back: 3, // Par défaut 3 mois
      region_bounds: {
        north: bounds.getNorthEast().lat(),
        south: bounds.getSouthWest().lat(),
        east: bounds.getNorthEast().lng(),
        west: bounds.getSouthWest().lng(),
      },
    };

    analysisMutation.mutate(request);
  };

  const handleDetectionSelect = (detection: any) => {
    setSelectedDetection(detection);
  };

  // Statistiques des détections
  const detectionStats = {
    total: detectionsData?.count || 0,
    recent: detectionsData?.results?.filter((d: any) => {
      const detectionDate = new Date(d.detection_date);
      const weekAgo = new Date();
      weekAgo.setDate(weekAgo.getDate() - 7);
      return detectionDate > weekAgo;
    }).length || 0,
    highConfidence: detectionsData?.results?.filter((d: any) => d.confidence_score >= 0.8).length || 0,
    pending: detectionsData?.results?.filter((d: any) => d.validation_status === 'DETECTED').length || 0,
  };

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 space-y-8">
      {/* En-tête */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-gradient-to-r from-blue-600 to-indigo-600 rounded-2xl p-8 text-white shadow-2xl"
      >
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold mb-2 flex items-center">
              <GlobeAltIcon className="w-8 h-8 mr-3" />
              Analyse Google Earth Engine
            </h1>
            <p className="text-blue-100 text-lg">
              Détection d'orpaillage par analyse spectrale en temps réel
            </p>
            <div className="flex items-center mt-2 space-x-4 text-sm text-blue-200">
              <span className="flex items-center">
                <BeakerIcon className="w-4 h-4 mr-1" />
                Sentinel-2
              </span>
              <span className="flex items-center">
                <MapIcon className="w-4 h-4 mr-1" />
                Côte d'Ivoire
              </span>
              {permissions.canLaunchAnalysis() && (
                <Badge variant="green" className="text-xs">
                  Analyse autorisée
                </Badge>
              )}
            </div>
          </div>
          <div className="hidden md:block">
            <div className="w-24 h-24 bg-white/20 rounded-full flex items-center justify-center">
              <GlobeAltIcon className="w-12 h-12 text-white" />
            </div>
          </div>
        </div>
      </motion.div>

      {/* Statistiques */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"
      >
        <StatCard
          title="Détections totales"
          value={detectionStats.total.toLocaleString()}
          icon={ChartBarIcon}
          color="blue"
        />
        <StatCard
          title="Cette semaine"
          value={detectionStats.recent.toLocaleString()}
          icon={ClockIcon}
          color="green"
          trend={detectionStats.recent > 0 ? { value: detectionStats.recent, isPositive: false } : undefined}
        />
        <StatCard
          title="Haute confiance"
          value={detectionStats.highConfidence.toLocaleString()}
          icon={CheckCircleIcon}
          color="purple"
        />
        <StatCard
          title="En attente validation"
          value={detectionStats.pending.toLocaleString()}
          icon={ExclamationTriangleIcon}
          color="orange"
        />
      </motion.div>

      {/* Carte principale */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
        className="h-96"
      >
        <GEEMap
          onAnalysisLaunch={handleAnalysisLaunch}
          onDetectionSelect={handleDetectionSelect}
          detections={detectionsData?.results || []}
          isAnalyzing={analysisMutation.isPending}
        />
      </motion.div>

      {/* Résultats d'analyse */}
      {analysisMutation.isPending && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
        >
          <Card className="p-6">
            <div className="flex items-center space-x-4">
              <div className="animate-spin">
                <BeakerIcon className="w-8 h-8 text-blue-500" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-slate-900">
                  Analyse en cours...
                </h3>
                <p className="text-slate-600">
                  Traitement des images satellites et calcul des indices spectraux
                </p>
              </div>
            </div>
          </Card>
        </motion.div>
      )}

      {analysisResults && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
        >
          <Card className="p-6">
            <div className="flex items-center space-x-4 mb-4">
              <CheckCircleIcon className="w-8 h-8 text-green-500" />
              <div>
                <h3 className="text-lg font-semibold text-slate-900">
                  Analyse terminée
                </h3>
                <p className="text-slate-600">
                  {new Date(analysisResults.data?.analysis_date).toLocaleString('fr-FR')}
                </p>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="bg-blue-50 rounded-lg p-4">
                <div className="text-2xl font-bold text-blue-600">
                  {analysisResults.data?.images_processed || 0}
                </div>
                <div className="text-sm text-blue-700">Images traitées</div>
              </div>
              
              <div className="bg-orange-50 rounded-lg p-4">
                <div className="text-2xl font-bold text-orange-600">
                  {analysisResults.data?.detections_found || 0}
                </div>
                <div className="text-sm text-orange-700">Nouvelles détections</div>
              </div>
              
              <div className="bg-red-50 rounded-lg p-4">
                <div className="text-2xl font-bold text-red-600">
                  {analysisResults.data?.alerts_generated || 0}
                </div>
                <div className="text-sm text-red-700">Alertes générées</div>
              </div>
              
              <div className="bg-purple-50 rounded-lg p-4">
                <div className="text-2xl font-bold text-purple-600">
                  {analysisResults.data?.investigations_created || 0}
                </div>
                <div className="text-sm text-purple-700">Investigations créées</div>
              </div>
            </div>
          </Card>
        </motion.div>
      )}

      {/* Détection sélectionnée */}
      {selectedDetection && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
        >
          <Card className="p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-slate-900">
                Détection sélectionnée #{selectedDetection.id}
              </h3>
              <Button
                variant="outline"
                size="sm"
                onClick={() => window.location.href = `/detections/${selectedDetection.id}`}
              >
                <EyeIcon className="w-4 h-4 mr-1" />
                Voir détails
              </Button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-slate-700 mb-1">
                  Type
                </label>
                <p className="text-slate-900">{selectedDetection.detection_type}</p>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-slate-700 mb-1">
                  Confiance
                </label>
                <p className="text-slate-900">
                  {(selectedDetection.confidence_score * 100).toFixed(1)}%
                </p>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-slate-700 mb-1">
                  Surface
                </label>
                <p className="text-slate-900">
                  {selectedDetection.area_hectares.toFixed(2)} ha
                </p>
              </div>
            </div>
          </Card>
        </motion.div>
      )}

      {/* Instructions */}
      {!permissions.canLaunchAnalysis() && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
        >
          <Card className="p-6 bg-yellow-50 border-yellow-200">
            <div className="flex items-center space-x-3">
              <ExclamationTriangleIcon className="w-6 h-6 text-yellow-600" />
              <div>
                <h3 className="font-semibold text-yellow-800">
                  Permissions insuffisantes
                </h3>
                <p className="text-yellow-700 text-sm">
                  Vous pouvez visualiser les détections existantes mais pas lancer de nouvelles analyses.
                  Contactez un administrateur pour obtenir les permissions nécessaires.
                </p>
              </div>
            </div>
          </Card>
        </motion.div>
      )}
    </div>
  );
};
