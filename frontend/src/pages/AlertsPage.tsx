import React, { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { motion } from 'framer-motion';
import {
  ExclamationTriangleIcon,
  BellIcon,
  CheckCircleIcon,
  XCircleIcon,
  ClockIcon,
  EyeIcon,
  UserPlusIcon,
  MapPinIcon,
  CalendarIcon,
  FunnelIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  DocumentTextIcon,
  ShieldCheckIcon,
  BellAlertIcon,
} from '@heroicons/react/24/outline';
import { useAuth } from '../contexts/AuthContext';
import { usePermissions } from '../hooks/usePermissions';
import alertService from '../services/alert.service';
import type { Alert, AlertFilters, AlertStats } from '../services/alert.service';
import { Card, Button, Loading, Modal } from '../components/ui';
import { AlertActionModal } from '../components/alerts/AlertActionModal';
import { AlertDetailsModal } from '../components/alerts/AlertDetailsModal';
import { AlertsMap } from '../components/alerts/AlertsMap';
import { NotificationCenter } from '../components/alerts/NotificationCenter';
import toast from 'react-hot-toast';

// Composant Badge de statut selon spécifications DoD
const AlertStatusBadge: React.FC<{ status: string }> = ({ status }) => {
  const getStatusConfig = () => {
    switch (status) {
      case 'ACTIVE':
        return {
          label: 'Active',
          className: 'bg-red-100 text-red-800 border-red-200',
          icon: ExclamationTriangleIcon,
        };
      case 'ACKNOWLEDGED':
        return {
          label: 'Accusée',
          className: 'bg-blue-100 text-blue-800 border-blue-200',
          icon: CheckCircleIcon,
        };
      case 'RESOLVED':
        return {
          label: 'Résolue',
          className: 'bg-green-100 text-green-800 border-green-200',
          icon: CheckCircleIcon,
        };
      case 'DISMISSED':
        return {
          label: 'Rejetée',
          className: 'bg-gray-100 text-gray-800 border-gray-200',
          icon: XCircleIcon,
        };
      default:
        return {
          label: status,
          className: 'bg-gray-100 text-gray-800 border-gray-200',
          icon: ClockIcon,
        };
    }
  };

  const config = getStatusConfig();
  const IconComponent = config.icon;

  return (
    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${config.className}`}>
      <IconComponent className="w-3 h-3 mr-1" />
      {config.label}
    </span>
  );
};

// Composant Badge de niveau de criticité
const AlertLevelBadge: React.FC<{ level: string }> = ({ level }) => {
  const getLevelConfig = () => {
    switch (level) {
      case 'CRITICAL':
        return {
          label: 'Critique',
          className: 'bg-red-600 text-white border-red-700',
          icon: ExclamationTriangleIcon,
        };
      case 'HIGH':
        return {
          label: 'Élevé',
          className: 'bg-orange-500 text-white border-orange-600',
          icon: ExclamationTriangleIcon,
        };
      case 'MEDIUM':
        return {
          label: 'Moyen',
          className: 'bg-yellow-500 text-white border-yellow-600',
          icon: ExclamationTriangleIcon,
        };
      case 'LOW':
        return {
          label: 'Faible',
          className: 'bg-green-500 text-white border-green-600',
          icon: CheckCircleIcon,
        };
      default:
        return {
          label: level,
          className: 'bg-gray-500 text-white border-gray-600',
          icon: ClockIcon,
        };
    }
  };

  const config = getLevelConfig();
  const IconComponent = config.icon;

  return (
    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${config.className}`}>
      <IconComponent className="w-3 h-3 mr-1" />
      {config.label}
    </span>
  );
};

export const AlertsPage: React.FC = () => {
  const { user } = useAuth();
  const permissions = usePermissions();
  const queryClient = useQueryClient();

  // États locaux
  const [selectedAlert, setSelectedAlert] = useState<Alert | null>(null);
  const [showActionModal, setShowActionModal] = useState(false);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [actionType, setActionType] = useState<'acknowledge' | 'resolve' | 'assign' | 'dismiss'>('acknowledge');
  const [currentPage, setCurrentPage] = useState(0);
  const [pageSize] = useState(20);
  const [activeTab, setActiveTab] = useState<'list' | 'map' | 'notifications'>('list');
  const [filters, setFilters] = useState<AlertFilters>({});

  // Récupération des données
  const {
    data: alertsData,
    isLoading,
    error,
    refetch: refetchAlerts
  } = useQuery({
    queryKey: ['alerts', filters, currentPage],
    queryFn: () => alertService.getAlerts(filters, currentPage + 1, pageSize),
    refetchInterval: 10000, // Refresh toutes les 10 secondes pour temps réel
  });

  const { data: alertStats } = useQuery({
    queryKey: ['alert-stats'],
    queryFn: () => alertService.getAlertStats(),
    refetchInterval: 30000, // Refresh toutes les 30 secondes
  });

  const { data: alertsByRegion } = useQuery({
    queryKey: ['alerts-by-region'],
    queryFn: () => alertService.getAlertsByRegion(),
    refetchInterval: 60000, // Refresh toutes les minutes
    enabled: activeTab === 'map',
  });

  // Mutations pour les actions sur les alertes
  const acknowledgeAlertMutation = useMutation({
    mutationFn: ({ id, notes }: { id: number; notes?: string }) =>
      alertService.acknowledgeAlert(id, notes),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['alerts'] });
      queryClient.invalidateQueries({ queryKey: ['alert-stats'] });
      setShowActionModal(false);
      setSelectedAlert(null);
      toast.success(data.message);
    },
    onError: (error: any) => {
      toast.error(error.message || 'Erreur lors de l\'accusé de réception');
    }
  });

  const resolveAlertMutation = useMutation({
    mutationFn: ({ id, notes }: { id: number; notes: string }) =>
      alertService.resolveAlert(id, notes),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['alerts'] });
      queryClient.invalidateQueries({ queryKey: ['alert-stats'] });
      setShowActionModal(false);
      setSelectedAlert(null);
      toast.success(data.message);
    },
    onError: (error: any) => {
      toast.error(error.message || 'Erreur lors de la résolution');
    }
  });

  const assignAlertMutation = useMutation({
    mutationFn: ({ id, assignedTo, notes }: { id: number; assignedTo: number; notes?: string }) =>
      alertService.assignAlert(id, assignedTo, notes),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['alerts'] });
      queryClient.invalidateQueries({ queryKey: ['alert-stats'] });
      setShowActionModal(false);
      setSelectedAlert(null);
      toast.success(`Alerte assignée à ${data.agent_info.name}`);
    },
    onError: (error: any) => {
      toast.error(error.message || 'Erreur lors de l\'assignation');
    }
  });

  const updateStatusMutation = useMutation({
    mutationFn: ({ id, status, notes }: { id: number; status: any; notes?: string }) =>
      alertService.updateAlertStatus(id, status, undefined, notes),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['alerts'] });
      queryClient.invalidateQueries({ queryKey: ['alert-stats'] });
      setShowActionModal(false);
      setSelectedAlert(null);
      toast.success(data.message);
    },
    onError: (error: any) => {
      toast.error(error.message || 'Erreur lors de la mise à jour');
    }
  });

  const markAsReadMutation = useMutation({
    mutationFn: ({ id, isRead }: { id: number; isRead: boolean }) =>
      alertService.markAsRead(id, isRead),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['alerts'] });
      queryClient.invalidateQueries({ queryKey: ['alert-stats'] });
    },
    onError: (error: any) => {
      toast.error(error.message || 'Erreur lors du marquage');
    }
  });

  // Gestionnaires d'événements
  const handleAlertAction = (alert: Alert, action: 'acknowledge' | 'resolve' | 'assign' | 'dismiss') => {
    setSelectedAlert(alert);
    setActionType(action);
    setShowActionModal(true);
  };

  const handleViewDetails = (alert: Alert) => {
    setSelectedAlert(alert);
    setShowDetailsModal(true);
    // Marquer comme lu si pas encore lu
    if (!alert.is_read) {
      markAsReadMutation.mutate({ id: alert.id, isRead: true });
    }
  };

  const handleActionSubmit = (data: any) => {
    if (!selectedAlert) return;

    switch (actionType) {
      case 'acknowledge':
        acknowledgeAlertMutation.mutate({ id: selectedAlert.id, notes: data.notes });
        break;
      case 'resolve':
        resolveAlertMutation.mutate({ id: selectedAlert.id, notes: data.notes });
        break;
      case 'assign':
        assignAlertMutation.mutate({
          id: selectedAlert.id,
          assignedTo: data.assignedTo,
          notes: data.notes
        });
        break;
      case 'dismiss':
        updateStatusMutation.mutate({
          id: selectedAlert.id,
          status: 'DISMISSED',
          notes: data.notes
        });
        break;
    }
  };

  const handleFilterChange = (key: string, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }));
    setCurrentPage(0); // Reset à la première page
  };

  // Données pour l'affichage
  const alerts = alertsData?.results || [];
  const totalCount = alertsData?.count || 0;
  const totalPages = Math.ceil(totalCount / pageSize);

  // Gestion des erreurs
  useEffect(() => {
    if (error) {
      toast.error('Erreur lors du chargement des alertes');
      console.error('Erreur alertes:', error);
    }
  }, [error]);

  // État de chargement
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Loading />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* En-tête */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-gradient-to-r from-red-600 to-red-800 rounded-2xl p-6 text-white shadow-xl"
      >
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold mb-2">
              Gestion des Alertes
            </h1>
            <p className="text-red-100">
              Surveillance et gestion des alertes de sécurité en temps réel
            </p>
          </div>
          <div className="hidden md:block">
            <div className="w-16 h-16 bg-gradient-to-r from-yellow-400 to-yellow-500 rounded-full flex items-center justify-center">
              <BellAlertIcon className="w-8 h-8 text-red-900" />
            </div>
          </div>
        </div>

        {/* Statistiques rapides */}
        {alertStats && (
          <div className="mt-4 grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="bg-red-500 bg-opacity-50 rounded-lg p-3">
              <div className="text-sm text-red-200">Total alertes</div>
              <div className="text-xl font-bold">{alertStats.total_alerts}</div>
            </div>
            <div className="bg-red-500 bg-opacity-50 rounded-lg p-3">
              <div className="text-sm text-red-200">Actives</div>
              <div className="text-xl font-bold">{alertStats.active_alerts}</div>
            </div>
            <div className="bg-red-500 bg-opacity-50 rounded-lg p-3">
              <div className="text-sm text-red-200">Critiques</div>
              <div className="text-xl font-bold">{alertStats.critical_alerts}</div>
            </div>
            <div className="bg-red-500 bg-opacity-50 rounded-lg p-3">
              <div className="text-sm text-red-200">Non lues</div>
              <div className="text-xl font-bold">{alertStats.unread_alerts}</div>
            </div>
          </div>
        )}
      </motion.div>

      {/* Navigation par onglets */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
      >
        <Card className="p-1">
          <div className="flex space-x-1">
            <button
              onClick={() => setActiveTab('list')}
              className={`flex-1 flex items-center justify-center px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                activeTab === 'list'
                  ? 'bg-red-100 text-red-700'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
              }`}
            >
              <BellIcon className="w-4 h-4 mr-2" />
              Liste Alertes ({totalCount})
            </button>
            <button
              onClick={() => setActiveTab('map')}
              className={`flex-1 flex items-center justify-center px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                activeTab === 'map'
                  ? 'bg-red-100 text-red-700'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
              }`}
            >
              <MapPinIcon className="w-4 h-4 mr-2" />
              Carte Alertes
            </button>
            <button
              onClick={() => setActiveTab('notifications')}
              className={`flex-1 flex items-center justify-center px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                activeTab === 'notifications'
                  ? 'bg-red-100 text-red-700'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
              }`}
            >
              <BellAlertIcon className="w-4 h-4 mr-2" />
              Notifications
            </button>
          </div>
        </Card>
      </motion.div>

      {/* Contenu des onglets */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
      >
        {activeTab === 'list' && (
          <div className="space-y-6">
            {/* Filtres */}
            <Card className="p-4">
              <div className="flex items-center mb-4">
                <FunnelIcon className="w-5 h-5 mr-2 text-gray-500" />
                <h3 className="text-lg font-semibold text-gray-900">Filtres</h3>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Niveau de criticité
                  </label>
                  <select
                    value={filters.level || ''}
                    onChange={(e) => handleFilterChange('level', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500"
                  >
                    <option value="">Tous les niveaux</option>
                    <option value="CRITICAL">Critique</option>
                    <option value="HIGH">Élevé</option>
                    <option value="MEDIUM">Moyen</option>
                    <option value="LOW">Faible</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Statut
                  </label>
                  <select
                    value={filters.alert_status || ''}
                    onChange={(e) => handleFilterChange('alert_status', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500"
                  >
                    <option value="">Tous les statuts</option>
                    <option value="ACTIVE">Active</option>
                    <option value="ACKNOWLEDGED">Accusée</option>
                    <option value="RESOLVED">Résolue</option>
                    <option value="DISMISSED">Rejetée</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Type d'alerte
                  </label>
                  <select
                    value={filters.alert_type || ''}
                    onChange={(e) => handleFilterChange('alert_type', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500"
                  >
                    <option value="">Tous les types</option>
                    <option value="MINING_DETECTION">Détection minière</option>
                    <option value="ENVIRONMENTAL_DAMAGE">Dommage environnemental</option>
                    <option value="SECURITY_BREACH">Violation sécurité</option>
                    <option value="SYSTEM_ERROR">Erreur système</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Statut lecture
                  </label>
                  <select
                    value={filters.is_read?.toString() || ''}
                    onChange={(e) => handleFilterChange('is_read', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500"
                  >
                    <option value="">Toutes</option>
                    <option value="false">Non lues</option>
                    <option value="true">Lues</option>
                  </select>
                </div>
              </div>
            </Card>

            {/* Liste des alertes */}
            <Card className="overflow-hidden">
              <div className="px-6 py-4 border-b border-gray-200">
                <h3 className="text-lg font-semibold text-gray-900">
                  Alertes ({totalCount})
                </h3>
              </div>

              {alerts.length === 0 ? (
                <div className="text-center py-12">
                  <BellIcon className="w-12 h-12 text-gray-300 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    Aucune alerte
                  </h3>
                  <p className="text-gray-500">
                    Aucune alerte ne correspond aux filtres sélectionnés.
                  </p>
                </div>
              ) : (
                <>
                  {/* Table responsive */}
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Alerte
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Niveau
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Statut
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Agent assigné
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Date
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Actions
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {alerts.map((alert, index) => (
                          <motion.tr
                            key={alert.id}
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ delay: index * 0.05 }}
                            className={`hover:bg-gray-50 transition-colors ${
                              !alert.is_read ? 'bg-blue-50' : ''
                            }`}
                          >
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div className="flex items-center">
                                <div className="flex-shrink-0 w-10 h-10 bg-red-100 rounded-full flex items-center justify-center">
                                  <span className="text-sm font-medium text-red-600">
                                    #{alert.id}
                                  </span>
                                </div>
                                <div className="ml-4">
                                  <div className="text-sm font-medium text-gray-900">
                                    {alert.name}
                                  </div>
                                  <div className="text-sm text-gray-500">
                                    {alert.detection_info?.type || alert.alert_type}
                                    {alert.detection_info?.area_hectares && (
                                      <span className="ml-2">
                                        • {alert.detection_info.area_hectares.toFixed(2)} ha
                                      </span>
                                    )}
                                  </div>
                                </div>
                              </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <AlertLevelBadge level={alert.level} />
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <AlertStatusBadge status={alert.alert_status} />
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              {alert.assigned_to_name ? (
                                <div className="flex items-center">
                                  <div className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center">
                                    <span className="text-xs font-medium text-gray-700">
                                      {alert.assigned_to_name.split(' ').map(n => n[0]).join('')}
                                    </span>
                                  </div>
                                  <span className="ml-2 text-sm text-gray-900">
                                    {alert.assigned_to_name}
                                  </span>
                                </div>
                              ) : (
                                <span className="text-sm text-gray-500">Non assignée</span>
                              )}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              <div className="flex items-center">
                                <CalendarIcon className="w-4 h-4 mr-1" />
                                {new Date(alert.sent_at).toLocaleDateString('fr-FR')}
                              </div>
                              <div className="text-xs text-gray-400">
                                {alert.time_since_created}
                              </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                              <div className="flex items-center space-x-2">
                                <button
                                  onClick={() => handleViewDetails(alert)}
                                  className="text-blue-600 hover:text-blue-900 p-1 rounded-full hover:bg-blue-100 transition-colors"
                                  title="Voir détails"
                                >
                                  <EyeIcon className="w-4 h-4" />
                                </button>

                                {/* Actions selon le statut */}
                                {alert.alert_status === 'ACTIVE' && (
                                  <button
                                    onClick={() => handleAlertAction(alert, 'acknowledge')}
                                    className="text-green-600 hover:text-green-900 p-1 rounded-full hover:bg-green-100 transition-colors"
                                    title="Accuser réception"
                                  >
                                    <CheckCircleIcon className="w-4 h-4" />
                                  </button>
                                )}

                                {(alert.alert_status === 'ACTIVE' || alert.alert_status === 'ACKNOWLEDGED') && (
                                  <>
                                    <button
                                      onClick={() => handleAlertAction(alert, 'assign')}
                                      className="text-purple-600 hover:text-purple-900 p-1 rounded-full hover:bg-purple-100 transition-colors"
                                      title="Assigner agent"
                                    >
                                      <UserPlusIcon className="w-4 h-4" />
                                    </button>
                                    <button
                                      onClick={() => handleAlertAction(alert, 'resolve')}
                                      className="text-green-600 hover:text-green-900 p-1 rounded-full hover:bg-green-100 transition-colors"
                                      title="Résoudre"
                                    >
                                      <ShieldCheckIcon className="w-4 h-4" />
                                    </button>
                                  </>
                                )}
                              </div>
                            </td>
                          </motion.tr>
                        ))}
                      </tbody>
                    </table>
                  </div>

                  {/* Pagination */}
                  {totalPages > 1 && (
                    <div className="px-6 py-4 border-t border-gray-200 flex items-center justify-between">
                      <div className="text-sm text-gray-700">
                        Page {currentPage + 1} sur {totalPages} • {totalCount} alerte{totalCount > 1 ? 's' : ''}
                      </div>
                      <div className="flex items-center space-x-2">
                        <button
                          onClick={() => setCurrentPage(prev => Math.max(0, prev - 1))}
                          disabled={currentPage === 0}
                          className="p-2 rounded-lg border border-gray-300 disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50 transition-colors"
                        >
                          <ChevronLeftIcon className="w-4 h-4" />
                        </button>
                        <button
                          onClick={() => setCurrentPage(prev => Math.min(totalPages - 1, prev + 1))}
                          disabled={currentPage === totalPages - 1}
                          className="p-2 rounded-lg border border-gray-300 disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50 transition-colors"
                        >
                          <ChevronRightIcon className="w-4 h-4" />
                        </button>
                      </div>
                    </div>
                  )}
                </>
              )}
            </Card>
          </div>
        )}

        {activeTab === 'map' && (
          <AlertsMap
            alertsByRegion={alertsByRegion || []}
            onAlertClick={handleViewDetails}
          />
        )}

        {activeTab === 'notifications' && (
          <NotificationCenter
            onAlertClick={handleViewDetails}
          />
        )}
      </motion.div>

      {/* Modals */}
      {selectedAlert && showActionModal && (
        <AlertActionModal
          isOpen={showActionModal}
          onClose={() => setShowActionModal(false)}
          alert={selectedAlert}
          actionType={actionType}
          onSubmit={handleActionSubmit}
          isLoading={
            acknowledgeAlertMutation.isPending ||
            resolveAlertMutation.isPending ||
            assignAlertMutation.isPending ||
            updateStatusMutation.isPending
          }
        />
      )}

      {selectedAlert && showDetailsModal && (
        <AlertDetailsModal
          isOpen={showDetailsModal}
          onClose={() => setShowDetailsModal(false)}
          alert={selectedAlert}
          onAction={handleAlertAction}
        />
      )}
    </div>
  );
};