import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  IconButton,
  Tooltip,
  CircularProgress,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  MenuItem
} from '@mui/material';
import {
  CheckCircle,
  Cancel,
  Visibility,
  Edit,
  Warning,
  Error,
  Info,
  Assignment,
  Person
} from '@mui/icons-material';
import { alertService } from '../services';

const AlertStatusChip = ({ status }: { status: string }) => {
  const getStatusColor = () => {
    switch (status.toLowerCase()) {
      case 'active':
        return 'error';
      case 'resolved':
        return 'success';
      case 'investigating':
        return 'warning';
      default:
        return 'default';
    }
  };

  const getStatusIcon = () => {
    switch (status.toLowerCase()) {
      case 'active':
        return <Error fontSize="small" />;
      case 'resolved':
        return <CheckCircle fontSize="small" />;
      case 'investigating':
        return <Warning fontSize="small" />;
      default:
        return <Info fontSize="small" />;
    }
  };

  return (
    <Chip
      icon={getStatusIcon()}
      label={status}
      color={getStatusColor()}
      size="small"
    />
  );
};

const AlertLevelChip = ({ level }: { level: string }) => {
  const getLevelColor = () => {
    switch (level.toLowerCase()) {
      case 'high':
        return 'error';
      case 'medium':
        return 'warning';
      case 'low':
        return 'info';
      default:
        return 'default';
    }
  };

  return (
    <Chip
      label={level}
      color={getLevelColor()}
      size="small"
    />
  );
};

export const AlertsPage = () => {
  const [selectedAlert, setSelectedAlert] = useState<any>(null);
  const [updateDialogOpen, setUpdateDialogOpen] = useState(false);
  const [assignDialogOpen, setAssignDialogOpen] = useState(false);
  const [newStatus, setNewStatus] = useState('');
  const [newComment, setNewComment] = useState('');
  const [selectedAgent, setSelectedAgent] = useState('');
  const [assignmentNotes, setAssignmentNotes] = useState('');
  const queryClient = useQueryClient();

  const { data: alerts, isLoading, error } = useQuery({
    queryKey: ['alerts'],
    queryFn: () => alertService.getActiveAlerts()
  });

  // Récupération de la liste des agents terrain
  const { data: agents } = useQuery({
    queryKey: ['field-agents'],
    queryFn: async () => {
      // Simulation d'agents terrain - à remplacer par un vrai service
      return [
        { id: 1, name: 'Agent Kouassi Jean', region: 'Bondoukou', workload: 3 },
        { id: 2, name: 'Agent Diabaté Marie', region: 'Bondoukou', workload: 1 },
        { id: 3, name: 'Agent Traoré Ibrahim', region: 'Tanda', workload: 2 },
        { id: 4, name: 'Agent Koné Fatou', region: 'Bouna', workload: 0 }
      ];
    }
  });

  const updateAlertMutation = useMutation({
    mutationFn: (data: { id: string; status: string; comment: string }) =>
      alertService.updateAlertStatus(data.id, data.status, data.comment),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['alerts'] });
      setUpdateDialogOpen(false);
      setSelectedAlert(null);
    }
  });

  const assignAlertMutation = useMutation({
    mutationFn: (data: { alertId: string; agentId: number; notes: string }) => {
      // Simulation d'attribution - à remplacer par un vrai service
      return Promise.resolve({
        success: true,
        message: `Alerte assignée à l'agent ${agents?.find(a => a.id === data.agentId)?.name}`
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['alerts'] });
      setAssignDialogOpen(false);
      setSelectedAlert(null);
      setSelectedAgent('');
      setAssignmentNotes('');
    }
  });

  const handleUpdateAlert = () => {
    if (selectedAlert && newStatus) {
      updateAlertMutation.mutate({
        id: selectedAlert.id,
        status: newStatus,
        comment: newComment
      });
    }
  };

  const handleAssignAlert = () => {
    if (selectedAlert && selectedAgent) {
      assignAlertMutation.mutate({
        alertId: selectedAlert.id,
        agentId: parseInt(selectedAgent),
        notes: assignmentNotes
      });
    }
  };

  if (isLoading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="60vh">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ mt: 2 }}>
        Erreur lors du chargement des alertes
      </Alert>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" component="h1" sx={{ fontWeight: 'bold', mb: 3 }}>
        Gestion des Alertes
      </Typography>

      <Card>
        <CardContent>
          <TableContainer component={Paper}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>ID</TableCell>
                  <TableCell>Type</TableCell>
                  <TableCell>Niveau</TableCell>
                  <TableCell>Statut</TableCell>
                  <TableCell>Agent Assigné</TableCell>
                  <TableCell>Date de Création</TableCell>
                  <TableCell>Dernière Mise à Jour</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {alerts?.results?.map((alert) => (
                  <TableRow key={alert.id}>
                    <TableCell>{alert.id}</TableCell>
                    <TableCell>{alert.detection_info?.type || alert.alert_type}</TableCell>
                    <TableCell><AlertLevelChip level={alert.level} /></TableCell>
                    <TableCell><AlertStatusChip status={alert.alert_status} /></TableCell>
                    <TableCell>
                      {alert.assigned_agent ? (
                        <Chip icon={<Person />} label={alert.assigned_agent} size="small" color="primary" />
                      ) : (
                        <Chip label="Non assigné" size="small" variant="outlined" />
                      )}
                    </TableCell>
                    <TableCell>{new Date(alert.sent_at).toLocaleString()}</TableCell>
                    <TableCell>{alert.time_since_created}</TableCell>
                    <TableCell>
                      <Box display="flex" gap={1}>
                        <Tooltip title="Voir les détails">
                          <IconButton size="small" onClick={() => setSelectedAlert(alert)}>
                            <Visibility />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="Assigner à un agent">
                          <IconButton size="small" onClick={() => {
                              setSelectedAlert(alert);
                              setSelectedAgent('');
                              setAssignmentNotes('');
                              setAssignDialogOpen(true);
                            }}>
                            <Assignment />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="Mettre à jour le statut">
                          <IconButton size="small" onClick={() => {
                              setSelectedAlert(alert);
                              setNewStatus('');
                              setNewComment('');
                              setUpdateDialogOpen(true);
                            }}>
                            <Edit />
                          </IconButton>
                        </Tooltip>
                      </Box>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>

      <Dialog
        open={updateDialogOpen}
        onClose={() => setUpdateDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>Mettre à jour le statut de l'alerte</DialogTitle>
        <DialogContent>
          <Box sx={{ mt: 2 }}>
            <TextField
              select
              fullWidth
              label="Nouveau statut"
              value={newStatus}
              onChange={(e) => setNewStatus(e.target.value)}
              sx={{ mb: 2 }}
            >
              <MenuItem value="active">Active</MenuItem>
              <MenuItem value="investigating">En investigation</MenuItem>
              <MenuItem value="resolved">Résolue</MenuItem>
            </TextField>
            <TextField
              fullWidth
              label="Commentaire"
              multiline
              rows={4}
              value={newComment}
              onChange={(e) => setNewComment(e.target.value)}
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setUpdateDialogOpen(false)}>Annuler</Button>
          <Button
            onClick={handleUpdateAlert}
            variant="contained"
            disabled={!newStatus}
          >
            Mettre à jour
          </Button>
        </DialogActions>
      </Dialog>

      {/* Dialogue d'attribution d'agent */}
      <Dialog open={assignDialogOpen} onClose={() => setAssignDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Assigner l'alerte à un agent terrain</DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 2 }}>
            <TextField
              select
              fullWidth
              label="Agent terrain"
              value={selectedAgent}
              onChange={(e) => setSelectedAgent(e.target.value)}
              margin="normal"
            >
              {agents?.map((agent) => (
                <MenuItem key={agent.id} value={agent.id.toString()}>
                  <Box display="flex" justifyContent="space-between" width="100%">
                    <span>{agent.name}</span>
                    <Box display="flex" alignItems="center" gap={1}>
                      <Chip label={agent.region} size="small" />
                      <Chip
                        label={`${agent.workload} tâches`}
                        size="small"
                        color={agent.workload > 2 ? 'warning' : 'success'}
                      />
                    </Box>
                  </Box>
                </MenuItem>
              ))}
            </TextField>
            <TextField
              fullWidth
              multiline
              rows={3}
              label="Notes d'attribution"
              value={assignmentNotes}
              onChange={(e) => setAssignmentNotes(e.target.value)}
              margin="normal"
              placeholder="Instructions spéciales, priorité, etc."
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setAssignDialogOpen(false)}>Annuler</Button>
          <Button
            onClick={handleAssignAlert}
            variant="contained"
            disabled={!selectedAgent || assignAlertMutation.isPending}
          >
            {assignAlertMutation.isPending ? 'Attribution...' : 'Assigner'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};