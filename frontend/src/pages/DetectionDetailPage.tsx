import React, { useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { motion } from 'framer-motion';
import {
  ArrowLeftIcon,
  MapPinIcon,
  CalendarIcon,
  CheckCircleIcon,
  XCircleIcon,
  ChatBubbleLeftRightIcon,
  DocumentTextIcon,
  PhotoIcon,
} from '@heroicons/react/24/outline';
import { usePermissions } from '../hooks/usePermissions';
import { Card, Button, Loading, Badge, Modal } from '../components/ui';
import detectionService from '../services/detection.service';
import {
  Detection,
  DETECTION_TYPE_LABELS,
  VALIDATION_STATUS_LABELS,
  DETECTION_TYPE_COLORS,
  VALIDATION_STATUS_COLORS,
  getConfidenceLevel,
  getDetectionTypeIcon,
  getValidationStatusIcon,
} from '../types/detection.types';
import toast from 'react-hot-toast';

export const DetectionDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const permissions = usePermissions();
  const queryClient = useQueryClient();
  
  const [showValidationModal, setShowValidationModal] = useState(false);
  const [validationStatus, setValidationStatus] = useState<string>('');
  const [validationComment, setValidationComment] = useState('');

  // Requête pour la détection
  const { data: detection, isLoading, error } = useQuery({
    queryKey: ['detection', id],
    queryFn: () => detectionService.getDetection(Number(id)),
    enabled: !!id,
  });

  // Mutation pour la validation
  const validateMutation = useMutation({
    mutationFn: ({ status, comment }: { status: string; comment?: string }) =>
      detectionService.validateDetection(Number(id), status, comment),
    onSuccess: (data) => {
      toast.success(data.message || 'Détection validée avec succès');
      queryClient.invalidateQueries({ queryKey: ['detection', id] });
      queryClient.invalidateQueries({ queryKey: ['detections'] });
      setShowValidationModal(false);
      setValidationComment('');
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error || 'Erreur lors de la validation');
    },
  });

  const handleValidation = () => {
    if (!validationStatus) return;
    
    validateMutation.mutate({
      status: validationStatus,
      comment: validationComment.trim() || undefined,
    });
  };

  const openValidationModal = (status: string) => {
    setValidationStatus(status);
    setShowValidationModal(true);
  };

  if (isLoading) {
    return <Loading />;
  }

  if (error || !detection) {
    return (
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <Card className="p-8 text-center">
          <h2 className="text-xl font-semibold text-slate-900 mb-2">
            Détection introuvable
          </h2>
          <p className="text-slate-600 mb-4">
            La détection demandée n'existe pas ou vous n'avez pas les permissions pour la voir.
          </p>
          <Button variant="outline" onClick={() => navigate('/detections')}>
            Retour aux détections
          </Button>
        </Card>
      </div>
    );
  }

  const confidenceLevel = getConfidenceLevel(detection.confidence_score);
  const canValidate = permissions.canValidateDetections() && 
                     detection.validation_status === 'DETECTED';

  return (
    <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* En-tête */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="mb-8"
      >
        <div className="flex items-center space-x-4 mb-4">
          <Button
            variant="ghost"
            onClick={() => navigate('/detections')}
            className="flex items-center space-x-2"
          >
            <ArrowLeftIcon className="w-4 h-4" />
            <span>Retour</span>
          </Button>
          <div className="h-6 w-px bg-slate-300"></div>
          <h1 className="text-2xl font-bold text-slate-900">
            Détection #{detection.id}
          </h1>
        </div>

        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <span className="text-2xl">
              {getDetectionTypeIcon(detection.detection_type)}
            </span>
            <span className="text-lg font-medium text-slate-900">
              {DETECTION_TYPE_LABELS[detection.detection_type]}
            </span>
          </div>
          <Badge
            variant={VALIDATION_STATUS_COLORS[detection.validation_status] as any}
          >
            {getValidationStatusIcon(detection.validation_status)} {VALIDATION_STATUS_LABELS[detection.validation_status]}
          </Badge>
          <Badge variant={confidenceLevel.color as any}>
            Confiance: {(detection.confidence_score * 100).toFixed(0)}%
          </Badge>
        </div>
      </motion.div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Colonne principale */}
        <div className="lg:col-span-2 space-y-6">
          {/* Informations générales */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
          >
            <Card className="p-6">
              <h2 className="text-lg font-semibold text-slate-900 mb-4">
                Informations générales
              </h2>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-1">
                    Localisation
                  </label>
                  <div className="flex items-center space-x-2">
                    <MapPinIcon className="w-4 h-4 text-slate-400" />
                    <span className="text-slate-900">{detection.region_name}</span>
                  </div>
                  <div className="text-sm text-slate-500 mt-1">
                    {detection.latitude.toFixed(6)}, {detection.longitude.toFixed(6)}
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-1">
                    Date de détection
                  </label>
                  <div className="flex items-center space-x-2">
                    <CalendarIcon className="w-4 h-4 text-slate-400" />
                    <span className="text-slate-900">
                      {new Date(detection.detection_date).toLocaleDateString('fr-FR', {
                        day: '2-digit',
                        month: 'long',
                        year: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit',
                      })}
                    </span>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-1">
                    Surface estimée
                  </label>
                  <span className="text-lg font-semibold text-slate-900">
                    {detection.area_hectares.toFixed(2)} hectares
                  </span>
                </div>

                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-1">
                    Version algorithme
                  </label>
                  <span className="text-slate-900 font-mono text-sm">
                    {detection.algorithm_version}
                  </span>
                </div>
              </div>
            </Card>
          </motion.div>

          {/* Indices spectraux */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
          >
            <Card className="p-6">
              <h2 className="text-lg font-semibold text-slate-900 mb-4">
                Analyse spectrale
              </h2>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="text-center">
                  <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                    <span className="text-2xl font-bold text-green-600">
                      {detection.ndvi_anomaly_score?.toFixed(2) || 'N/A'}
                    </span>
                  </div>
                  <h3 className="font-medium text-slate-900">NDVI</h3>
                  <p className="text-sm text-slate-600">Indice de végétation</p>
                </div>

                <div className="text-center">
                  <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                    <span className="text-2xl font-bold text-blue-600">
                      {detection.ndwi_anomaly_score?.toFixed(2) || 'N/A'}
                    </span>
                  </div>
                  <h3 className="font-medium text-slate-900">NDWI</h3>
                  <p className="text-sm text-slate-600">Indice d'eau</p>
                </div>

                <div className="text-center">
                  <div className="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-3">
                    <span className="text-2xl font-bold text-orange-600">
                      {detection.ndti_anomaly_score?.toFixed(2) || 'N/A'}
                    </span>
                  </div>
                  <h3 className="font-medium text-slate-900">NDTI</h3>
                  <p className="text-sm text-slate-600">Indice de sol nu</p>
                </div>
              </div>
            </Card>
          </motion.div>

          {/* Image satellite */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
          >
            <Card className="p-6">
              <h2 className="text-lg font-semibold text-slate-900 mb-4">
                Image satellite
              </h2>
              
              <div className="bg-slate-100 rounded-lg p-8 text-center">
                <PhotoIcon className="w-16 h-16 text-slate-400 mx-auto mb-4" />
                <p className="text-slate-600">
                  Image: {detection.image_name}
                </p>
                <p className="text-sm text-slate-500">
                  Capturée le {new Date(detection.image_capture_date).toLocaleDateString('fr-FR')}
                </p>
              </div>
            </Card>
          </motion.div>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Actions de validation */}
          {canValidate && (
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.1 }}
            >
              <Card className="p-6">
                <h3 className="text-lg font-semibold text-slate-900 mb-4">
                  Validation
                </h3>
                
                <div className="space-y-3">
                  <Button
                    variant="primary"
                    className="w-full flex items-center justify-center space-x-2"
                    onClick={() => openValidationModal('VALIDATED')}
                    disabled={validateMutation.isPending}
                  >
                    <CheckCircleIcon className="w-4 h-4" />
                    <span>Valider la détection</span>
                  </Button>
                  
                  <Button
                    variant="outline"
                    className="w-full flex items-center justify-center space-x-2 text-red-600 hover:text-red-700 hover:bg-red-50"
                    onClick={() => openValidationModal('FALSE_POSITIVE')}
                    disabled={validateMutation.isPending}
                  >
                    <XCircleIcon className="w-4 h-4" />
                    <span>Marquer faux positif</span>
                  </Button>
                </div>
              </Card>
            </motion.div>
          )}

          {/* Informations de validation */}
          {detection.validated_by_name && (
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.2 }}
            >
              <Card className="p-6">
                <h3 className="text-lg font-semibold text-slate-900 mb-4">
                  Historique de validation
                </h3>
                
                <div className="space-y-3">
                  <div>
                    <label className="block text-sm font-medium text-slate-700 mb-1">
                      Validé par
                    </label>
                    <span className="text-slate-900">{detection.validated_by_name}</span>
                  </div>
                  
                  {detection.validated_at && (
                    <div>
                      <label className="block text-sm font-medium text-slate-700 mb-1">
                        Date de validation
                      </label>
                      <span className="text-slate-900">
                        {new Date(detection.validated_at).toLocaleString('fr-FR')}
                      </span>
                    </div>
                  )}
                </div>
              </Card>
            </motion.div>
          )}

          {/* Actions supplémentaires */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.3 }}
          >
            <Card className="p-6">
              <h3 className="text-lg font-semibold text-slate-900 mb-4">
                Actions
              </h3>
              
              <div className="space-y-3">
                <Button
                  variant="outline"
                  className="w-full flex items-center justify-center space-x-2"
                  onClick={() => toast.info('Fonctionnalité en développement')}
                >
                  <DocumentTextIcon className="w-4 h-4" />
                  <span>Générer rapport</span>
                </Button>
                
                <Button
                  variant="outline"
                  className="w-full flex items-center justify-center space-x-2"
                  onClick={() => toast.info('Fonctionnalité en développement')}
                >
                  <ChatBubbleLeftRightIcon className="w-4 h-4" />
                  <span>Créer investigation</span>
                </Button>
              </div>
            </Card>
          </motion.div>
        </div>
      </div>

      {/* Modal de validation */}
      <Modal
        isOpen={showValidationModal}
        onClose={() => setShowValidationModal(false)}
        title="Validation de la détection"
      >
        <div className="space-y-4">
          <p className="text-slate-600">
            Vous êtes sur le point de marquer cette détection comme{' '}
            <span className="font-medium">
              {validationStatus === 'VALIDATED' ? 'validée' : 'faux positif'}
            </span>.
          </p>
          
          <div>
            <label className="block text-sm font-medium text-slate-700 mb-2">
              Commentaire (optionnel)
            </label>
            <textarea
              value={validationComment}
              onChange={(e) => setValidationComment(e.target.value)}
              rows={3}
              className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Ajoutez un commentaire sur votre décision..."
            />
          </div>
          
          <div className="flex space-x-3 pt-4">
            <Button
              variant="primary"
              onClick={handleValidation}
              loading={validateMutation.isPending}
              className="flex-1"
            >
              Confirmer
            </Button>
            <Button
              variant="outline"
              onClick={() => setShowValidationModal(false)}
              className="flex-1"
            >
              Annuler
            </Button>
          </div>
        </div>
      </Modal>
    </div>
  );
};
