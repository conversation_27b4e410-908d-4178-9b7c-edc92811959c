import React, { createContext, useContext, useState, useEffect } from 'react';
import { authService } from '../services/auth.service';
import type { User, LoginResponse } from '../services/auth.service';
import { useNavigate } from 'react-router-dom';
import toast from 'react-hot-toast';

type AuthContextType = {
  user: User | null;
  loading: boolean;
  error: string | null;
  login: (email: string, password: string) => Promise<void>;
  logout: () => Promise<void>;
  refreshToken: () => Promise<void>;
  isAuthenticated: boolean;
  hasAuthority: (authorityName: string) => boolean;
  hasRole: (role: string) => boolean;
  hasPermission: (permission: string) => boolean;
  // Permissions spécifiques
  canViewStats: () => boolean;
  canLaunchAnalysis: () => boolean;
  canManageInvestigations: () => boolean;
  canViewFinancialRisks: () => boolean;
  isResponsableRegional: () => boolean;
  isAgentTerrain: () => boolean;
  isAdmin: () => boolean;
};

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Auto-refresh token toutes les 14 minutes (token expire en 15 min)
  useEffect(() => {
    let refreshInterval: NodeJS.Timeout;

    if (user && authService.isAuthenticated()) {
      refreshInterval = setInterval(async () => {
        try {
          await authService.refreshToken();
        } catch (error) {
          console.error('Erreur refresh token:', error);
          await logout();
        }
      }, 14 * 60 * 1000); // 14 minutes
    }

    return () => {
      if (refreshInterval) {
        clearInterval(refreshInterval);
      }
    };
  }, [user]);

  useEffect(() => {
    const initAuth = async () => {
      try {
        if (authService.isAuthenticated()) {
          const userData = await authService.getUserProfile();
          setUser(userData);
        }
      } catch (error) {
        console.error('Erreur chargement profil:', error);
        authService.logout();
      } finally {
        setLoading(false);
      }
    };

    initAuth();
  }, []);

  const login = async (email: string, password: string) => {
    try {
      setLoading(true);
      setError(null);
      const response = await authService.login(email, password);
      setUser(response.user);

      // Redirection selon rôle
      const redirectPath = getRedirectPath(response.user);
      window.location.href = redirectPath;

      toast.success(`Bienvenue, ${response.user.full_name}!`);
    } catch (error: any) {
      setError(error.message);
      toast.error(error.message);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const logout = async () => {
    try {
      await authService.logout();
      setUser(null);
      setError(null);
      window.location.href = '/login';
      toast.success('Déconnexion réussie');
    } catch (error: any) {
      console.error('Erreur logout:', error);
      // Force logout même en cas d'erreur
      authService.logout();
      setUser(null);
      window.location.href = '/login';
    }
  };

  const refreshToken = async () => {
    try {
      await authService.refreshToken();
    } catch (error) {
      await logout();
      throw error;
    }
  };

  // Fonction utilitaire pour redirection selon rôle
  const getRedirectPath = (user: User): string => {
    if (authService.hasAuthority(user, 'Responsable Régional')) {
      return '/dashboard';
    } else if (authService.hasAuthority(user, 'Agent Terrain')) {
      return '/investigations'; // Mes investigations
    } else if (authService.hasAnyAuthority(user, ['Agent Analyste', 'Agent Technique'])) {
      return '/dashboard';
    } else if (authService.hasAuthority(user, 'Administrateur')) {
      return '/dashboard';
    }
    return '/dashboard'; // Par défaut
  };

  // Méthodes de permissions
  const hasAuthority = (authorityName: string): boolean => {
    return authService.hasAuthority(user, authorityName);
  };

  const hasRole = (role: string): boolean => {
    return authService.hasAuthority(user, role);
  };

  const hasPermission = (permission: string): boolean => {
    // Logique permissions selon backend
    switch (permission) {
      case 'VIEW_STATS':
        return authService.canViewStats(user);
      case 'LAUNCH_ANALYSIS':
        return authService.canLaunchAnalysis(user);
      case 'MANAGE_INVESTIGATIONS':
        return authService.canManageInvestigations(user);
      case 'VIEW_FINANCIAL_RISKS':
        return authService.canViewFinancialRisks(user);
      default:
        return false;
    }
  };

  // Permissions spécifiques
  const canViewStats = () => authService.canViewStats(user);
  const canLaunchAnalysis = () => authService.canLaunchAnalysis(user);
  const canManageInvestigations = () => authService.canManageInvestigations(user);
  const canViewFinancialRisks = () => authService.canViewFinancialRisks(user);
  const isResponsableRegional = () => authService.hasAuthority(user, 'Responsable Régional');
  const isAgentTerrain = () => authService.hasAuthority(user, 'Agent Terrain');
  const isAdmin = () => authService.isAdmin(user);

  const value = {
    user,
    loading,
    error,
    login,
    logout,
    refreshToken,
    isAuthenticated: !!user,
    hasAuthority,
    hasRole,
    hasPermission,
    // Permissions spécifiques
    canViewStats,
    canLaunchAnalysis,
    canManageInvestigations,
    canViewFinancialRisks,
    isResponsableRegional,
    isAgentTerrain,
    isAdmin,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth doit être utilisé à l\'intérieur d\'un AuthProvider');
  }
  return context;
};