import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Toaster } from 'react-hot-toast';
import { AuthProvider } from './contexts/AuthContext';
import { ProtectedRoute } from './components/ProtectedRoute';
import { MainLayout } from './layouts/MainLayout';
import { LoginPage } from './pages/LoginPage';
import { DashboardPage } from './pages/DashboardPage';
import { ImagesPage } from './pages/ImagesPage';
import { DetectionsPage } from './pages/DetectionsPage';
import { DetectionsListPage } from './pages/DetectionsListPage';
import { DetectionDetailPage } from './pages/DetectionDetailPage';
import { AlertsPage } from './pages/AlertsPage';
import { InvestigationsPage } from './pages/InvestigationsPage';
import { StatsPage } from './pages/StatsPage';

// Création du client React Query
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
      retry: 1,
    },
  },
});

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        <Router>
          <Toaster
            position="top-right"
            toastOptions={{
              duration: 4000,
              style: {
                background: '#fff',
                color: '#0B1E3F',
                border: '1px solid #E2E8F0',
                borderRadius: '12px',
                boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
              },
              success: {
                iconTheme: {
                  primary: '#10B981',
                  secondary: '#fff',
                },
              },
              error: {
                iconTheme: {
                  primary: '#E63946',
                  secondary: '#fff',
                },
              },
            }}
          />
          <Routes>
            {/* Route publique */}
            <Route path="/login" element={<LoginPage />} />

            {/* Route par défaut - redirection intelligente */}
            <Route
              path="/"
              element={
                <ProtectedRoute>
                  <MainLayout>
                    <DashboardPage />
                  </MainLayout>
                </ProtectedRoute>
              }
            />

            {/* Dashboard principal - Responsables, Analystes, Techniques, Admin */}
            <Route
              path="/dashboard"
              element={
                <ProtectedRoute requiredPermissions={['VIEW_STATS']}>
                  <MainLayout>
                    <DashboardPage />
                  </MainLayout>
                </ProtectedRoute>
              }
            />

            {/* Images satellites - Tous les utilisateurs authentifiés */}
            <Route
              path="/images"
              element={
                <ProtectedRoute>
                  <MainLayout>
                    <ImagesPage />
                  </MainLayout>
                </ProtectedRoute>
              }
            />

            {/* Détections - Responsables Régionaux uniquement */}
            <Route
              path="/detections"
              element={
                <ProtectedRoute requiredRoles={['Responsable Régional']}>
                  <MainLayout>
                    <DetectionsListPage />
                  </MainLayout>
                </ProtectedRoute>
              }
            />
            <Route
              path="/detections/:id"
              element={
                <ProtectedRoute requiredRoles={['Responsable Régional']}>
                  <MainLayout>
                    <DetectionDetailPage />
                  </MainLayout>
                </ProtectedRoute>
              }
            />

            {/* Alertes - Responsables + Agents Terrain */}
            <Route
              path="/alerts"
              element={
                <ProtectedRoute requiredRoles={['Responsable Régional', 'Agent Terrain']}>
                  <MainLayout>
                    <AlertsPage />
                  </MainLayout>
                </ProtectedRoute>
              }
            />

            {/* Investigations - Responsables + Agents Terrain */}
            <Route
              path="/investigations"
              element={
                <ProtectedRoute requiredPermissions={['MANAGE_INVESTIGATIONS']}>
                  <MainLayout>
                    <InvestigationsPage />
                  </MainLayout>
                </ProtectedRoute>
              }
            />

            {/* Statistiques avancées - Responsables, Analystes, Techniques, Admin */}
            <Route
              path="/stats"
              element={
                <ProtectedRoute requiredPermissions={['VIEW_STATS']}>
                  <MainLayout>
                    <StatsPage />
                  </MainLayout>
                </ProtectedRoute>
              }
            />

            {/* Route 404 - Redirection vers dashboard */}
            <Route
              path="*"
              element={
                <ProtectedRoute>
                  <MainLayout>
                    <DashboardPage />
                  </MainLayout>
                </ProtectedRoute>
              }
            />
          </Routes>
        </Router>
      </AuthProvider>
    </QueryClientProvider>
  );
}

export default App;
