import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Toaster } from 'react-hot-toast';
import { AuthProvider } from './contexts/AuthContext';
import { ProtectedRoute } from './components/ProtectedRoute';
import { MainLayout } from './layouts/MainLayout';
import { LoginPage } from './pages/LoginPage';
import { DashboardPage } from './pages/DashboardPage';
import { ImagesPage } from './pages/ImagesPage';
import { DetectionsPage } from './pages/DetectionsPage';
import { DetectionsListPage } from './pages/DetectionsListPage';
import { DetectionDetailPage } from './pages/DetectionDetailPage';
import { AlertsPage } from './pages/AlertsPage';
import { InvestigationsPage } from './pages/InvestigationsPage';
import { ReportsPage } from './pages/ReportsPage';
import { RegionsPage } from './pages/RegionsPage';
import { AccountPage } from './pages/AccountPage';
import { SpectralAnalysisPage } from './pages/SpectralAnalysisPage';
import { GEEAnalysisPage } from './pages/GEEAnalysisPage';

// Création du client React Query
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
      retry: 1,
    },
  },
});

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        <Router>
          <Toaster
            position="top-right"
            toastOptions={{
              duration: 4000,
              style: {
                background: '#fff',
                color: '#0B1E3F',
                border: '1px solid #E2E8F0',
                borderRadius: '12px',
                boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
              },
              success: {
                iconTheme: {
                  primary: '#10B981',
                  secondary: '#fff',
                },
              },
              error: {
                iconTheme: {
                  primary: '#E63946',
                  secondary: '#fff',
                },
              },
            }}
          />
          <Routes>
            <Route path="/login" element={<LoginPage />} />
            <Route
              path="/"
              element={
                <ProtectedRoute>
                  <MainLayout>
                    <DashboardPage />
                  </MainLayout>
                </ProtectedRoute>
              }
            />
            <Route
              path="/images"
              element={
                <ProtectedRoute>
                  <MainLayout>
                    <ImagesPage />
                  </MainLayout>
                </ProtectedRoute>
              }
            />
            <Route
              path="/detections"
              element={
                <ProtectedRoute>
                  <MainLayout>
                    <DetectionsListPage />
                  </MainLayout>
                </ProtectedRoute>
              }
            />
            <Route
              path="/detections/:id"
              element={
                <ProtectedRoute>
                  <MainLayout>
                    <DetectionDetailPage />
                  </MainLayout>
                </ProtectedRoute>
              }
            />
            <Route
              path="/alerts"
              element={
                <ProtectedRoute>
                  <MainLayout>
                    <AlertsPage />
                  </MainLayout>
                </ProtectedRoute>
              }
            />
            <Route
              path="/investigations"
              element={
                <ProtectedRoute>
                  <MainLayout>
                    <InvestigationsPage />
                  </MainLayout>
                </ProtectedRoute>
              }
            />
            <Route
              path="/reports"
              element={
                <ProtectedRoute>
                  <MainLayout>
                    <ReportsPage />
                  </MainLayout>
                </ProtectedRoute>
              }
            />
            <Route
              path="/stats"
              element={
                <ProtectedRoute>
                  <MainLayout>
                    {/* TODO: Ajouter la page des statistiques */}
                    <div>Statistiques</div>
                  </MainLayout>
                </ProtectedRoute>
              }
            />
            <Route
              path="/regions"
              element={
                <ProtectedRoute>
                  <MainLayout>
                    <RegionsPage />
                  </MainLayout>
                </ProtectedRoute>
              }
            />
            <Route
              path="/account"
              element={
                <ProtectedRoute>
                  <MainLayout>
                    <AccountPage />
                  </MainLayout>
                </ProtectedRoute>
              }
            />
            <Route
              path="/spectral"
              element={
                <ProtectedRoute>
                  <MainLayout>
                    <SpectralAnalysisPage />
                  </MainLayout>
                </ProtectedRoute>
              }
            />
            <Route
              path="/gee-analysis"
              element={
                <ProtectedRoute>
                  <MainLayout>
                    <GEEAnalysisPage />
                  </MainLayout>
                </ProtectedRoute>
              }
            />
          </Routes>
        </Router>
      </AuthProvider>
    </QueryClientProvider>
  );
}

export default App;
