import React, { useEffect, useRef, useState } from 'react';
import L from 'leaflet';
import 'leaflet/dist/leaflet.css';
import 'leaflet-draw/dist/leaflet.draw.css';
import 'leaflet-draw';
import { AnalysisZone } from '../../services/analysis.service';
import { Button } from '../ui';

// Fix pour les icônes Leaflet
delete (L.Icon.Default.prototype as any)._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
});

interface ZoneSelectionMapProps {
  zones: AnalysisZone[];
  onZoneSelected: (zone: AnalysisZone) => void;
  center: [number, number];
  zoom: number;
  className?: string;
}

export const ZoneSelectionMap: React.FC<ZoneSelectionMapProps> = ({
  zones,
  onZoneSelected,
  center,
  zoom,
  className = '',
}) => {
  const mapRef = useRef<HTMLDivElement>(null);
  const mapInstanceRef = useRef<L.Map | null>(null);
  const drawnItemsRef = useRef<L.FeatureGroup | null>(null);
  const [isDrawing, setIsDrawing] = useState(false);
  const [customZoneName, setCustomZoneName] = useState('');

  useEffect(() => {
    if (!mapRef.current) return;

    // Initialiser la carte
    if (!mapInstanceRef.current) {
      mapInstanceRef.current = L.map(mapRef.current).setView(center, zoom);

      // Ajouter les tuiles OpenStreetMap
      L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '© OpenStreetMap contributors',
        maxZoom: 18,
      }).addTo(mapInstanceRef.current);

      // Initialiser le groupe pour les dessins
      drawnItemsRef.current = new L.FeatureGroup();
      mapInstanceRef.current.addLayer(drawnItemsRef.current);

      // Configurer les outils de dessin
      const drawControl = new L.Control.Draw({
        position: 'topright',
        draw: {
          polygon: {
            allowIntersection: false,
            drawError: {
              color: '#e1e100',
              message: '<strong>Erreur:</strong> Les lignes ne peuvent pas se croiser!',
            },
            shapeOptions: {
              color: '#97009c',
              fillOpacity: 0.2,
            },
          },
          rectangle: {
            shapeOptions: {
              color: '#97009c',
              fillOpacity: 0.2,
            },
          },
          circle: false,
          marker: false,
          circlemarker: false,
          polyline: false,
        },
        edit: {
          featureGroup: drawnItemsRef.current,
          remove: true,
        },
      });

      mapInstanceRef.current.addControl(drawControl);

      // Gestionnaires d'événements pour le dessin
      mapInstanceRef.current.on(L.Draw.Event.DRAWSTART, () => {
        setIsDrawing(true);
      });

      mapInstanceRef.current.on(L.Draw.Event.CREATED, (e: any) => {
        const layer = e.layer;
        drawnItemsRef.current!.addLayer(layer);
        setIsDrawing(false);

        // Calculer les coordonnées et la surface
        const coordinates = layer.getLatLngs()[0].map((latlng: L.LatLng) => [
          latlng.lat,
          latlng.lng,
        ]);

        // Calculer la surface approximative en hectares
        const area = L.GeometryUtil ? L.GeometryUtil.geodesicArea(layer.getLatLngs()[0]) / 10000 : 0;

        // Calculer le centre
        const bounds = layer.getBounds();
        const center_lat = bounds.getCenter().lat;
        const center_lng = bounds.getCenter().lng;

        // Demander le nom de la zone
        const name = prompt('Nom de la zone personnalisée:') || 'Zone personnalisée';

        const customZone: AnalysisZone = {
          name,
          coordinates,
          area_hectares: area,
          center_lat,
          center_lng,
        };

        onZoneSelected(customZone);
      });
    }

    return () => {
      if (mapInstanceRef.current) {
        mapInstanceRef.current.remove();
        mapInstanceRef.current = null;
      }
    };
  }, [center, zoom, onZoneSelected]);

  // Afficher les zones prédéfinies
  useEffect(() => {
    if (!mapInstanceRef.current || !zones.length) return;

    zones.forEach((zone) => {
      if (zone.coordinates && zone.coordinates.length > 0) {
        const polygon = L.polygon(zone.coordinates as L.LatLngExpression[], {
          color: '#3B82F6',
          fillColor: '#3B82F6',
          fillOpacity: 0.2,
          weight: 2,
        }).addTo(mapInstanceRef.current!);

        // Popup avec informations de la zone
        polygon.bindPopup(`
          <div class="p-2">
            <h3 class="font-bold text-lg">${zone.name}</h3>
            <p class="text-sm text-gray-600">Surface: ${zone.area_hectares.toFixed(2)} ha</p>
            <button 
              onclick="window.selectZone('${zone.id || zone.name}')" 
              class="mt-2 px-3 py-1 bg-blue-500 text-white rounded text-sm hover:bg-blue-600"
            >
              Sélectionner cette zone
            </button>
          </div>
        `);

        // Gestionnaire de clic pour sélection
        polygon.on('click', () => {
          onZoneSelected(zone);
        });
      }
    });

    // Fonction globale pour sélection depuis popup
    (window as any).selectZone = (zoneId: string) => {
      const zone = zones.find(z => (z.id?.toString() || z.name) === zoneId);
      if (zone) {
        onZoneSelected(zone);
      }
    };
  }, [zones, onZoneSelected]);

  return (
    <div className={`relative ${className}`}>
      <div ref={mapRef} className="w-full h-96 rounded-lg border border-gray-300" />
      
      {/* Instructions */}
      <div className="absolute top-4 left-4 bg-white rounded-lg shadow-lg p-3 z-[1000] max-w-xs">
        <h4 className="font-semibold text-sm mb-2 text-gray-900">Instructions</h4>
        <div className="space-y-1 text-xs text-gray-600">
          <div>• Cliquez sur une zone bleue pour la sélectionner</div>
          <div>• Utilisez les outils à droite pour dessiner une zone personnalisée</div>
          <div>• Rectangle ou polygone selon vos besoins</div>
        </div>
      </div>

      {/* Zones prédéfinies */}
      {zones.length > 0 && (
        <div className="absolute bottom-4 left-4 bg-white rounded-lg shadow-lg p-3 z-[1000]">
          <h4 className="font-semibold text-sm mb-2 text-gray-900">Zones prédéfinies</h4>
          <div className="space-y-1 max-h-32 overflow-y-auto">
            {zones.map((zone, index) => (
              <button
                key={zone.id || index}
                onClick={() => onZoneSelected(zone)}
                className="block w-full text-left px-2 py-1 text-xs text-gray-700 hover:bg-gray-100 rounded"
              >
                <div className="font-medium">{zone.name}</div>
                <div className="text-gray-500">{zone.area_hectares.toFixed(2)} ha</div>
              </button>
            ))}
          </div>
        </div>
      )}

      {/* Indicateur de dessin */}
      {isDrawing && (
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-purple-600 text-white px-4 py-2 rounded-lg shadow-lg z-[1000]">
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-white rounded-full animate-pulse"></div>
            <span className="text-sm font-medium">Dessinez votre zone d'analyse...</span>
          </div>
        </div>
      )}
    </div>
  );
};
