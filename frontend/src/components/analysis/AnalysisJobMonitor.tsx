import React, { useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import {
  XMarkIcon,
  BeakerIcon,
  ClockIcon,
  PlayIcon,
  CheckCircleIcon,
  XCircleIcon,
  StopIcon,
  DocumentArrowDownIcon,
  MapPinIcon,
  CalendarIcon,
  SatelliteIcon,
} from '@heroicons/react/24/outline';
import { AnalysisJob } from '../../services/analysis.service';
import analysisService from '../../services/analysis.service';
import { Modal, Button, Loading } from '../ui';

interface AnalysisJobMonitorProps {
  job: AnalysisJob;
  onClose: () => void;
  onCancel: (jobId: string) => void;
  onDownloadReport: (jobId: string) => void;
}

export const AnalysisJobMonitor: React.FC<AnalysisJobMonitorProps> = ({
  job: initialJob,
  onClose,
  onCancel,
  onDownloadReport,
}) => {
  const [job, setJob] = useState(initialJob);
  const [results, setResults] = useState<any>(null);
  const [isLoadingResults, setIsLoadingResults] = useState(false);

  // Mise à jour temps réel du job
  useEffect(() => {
    if (job.status === 'RUNNING' || job.status === 'PENDING') {
      const interval = setInterval(async () => {
        try {
          const updatedJob = await analysisService.getAnalysisJob(job.id);
          setJob(updatedJob);
        } catch (error) {
          console.error('Erreur mise à jour job:', error);
        }
      }, 2000); // Mise à jour toutes les 2 secondes

      return () => clearInterval(interval);
    }
  }, [job.id, job.status]);

  // Charger les résultats si le job est terminé
  useEffect(() => {
    if (job.status === 'COMPLETED' && !results) {
      setIsLoadingResults(true);
      analysisService.getAnalysisResults(job.id)
        .then(setResults)
        .catch(console.error)
        .finally(() => setIsLoadingResults(false));
    }
  }, [job.status, job.id, results]);

  const getStatusIcon = () => {
    switch (job.status) {
      case 'PENDING':
        return <ClockIcon className="w-6 h-6 text-yellow-500" />;
      case 'RUNNING':
        return <PlayIcon className="w-6 h-6 text-blue-500" />;
      case 'COMPLETED':
        return <CheckCircleIcon className="w-6 h-6 text-green-500" />;
      case 'FAILED':
        return <XCircleIcon className="w-6 h-6 text-red-500" />;
      case 'CANCELLED':
        return <StopIcon className="w-6 h-6 text-gray-500" />;
      default:
        return <BeakerIcon className="w-6 h-6 text-gray-500" />;
    }
  };

  const getStatusLabel = () => {
    switch (job.status) {
      case 'PENDING':
        return 'En attente';
      case 'RUNNING':
        return 'En cours d\'exécution';
      case 'COMPLETED':
        return 'Terminée avec succès';
      case 'FAILED':
        return 'Échouée';
      case 'CANCELLED':
        return 'Annulée';
      default:
        return job.status;
    }
  };

  const formatDuration = (start: string, end?: string) => {
    const startTime = new Date(start);
    const endTime = end ? new Date(end) : new Date();
    const duration = endTime.getTime() - startTime.getTime();
    
    const hours = Math.floor(duration / (1000 * 60 * 60));
    const minutes = Math.floor((duration % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((duration % (1000 * 60)) / 1000);
    
    if (hours > 0) return `${hours}h ${minutes}m ${seconds}s`;
    if (minutes > 0) return `${minutes}m ${seconds}s`;
    return `${seconds}s`;
  };

  return (
    <Modal isOpen={true} onClose={onClose} size="xl">
      <div className="p-6">
        {/* En-tête */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center">
              {getStatusIcon()}
            </div>
            <div>
              <h2 className="text-xl font-bold text-gray-900">{job.name}</h2>
              <p className="text-sm text-gray-600">{getStatusLabel()}</p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-full transition-colors"
          >
            <XMarkIcon className="w-5 h-5 text-gray-500" />
          </button>
        </div>

        {/* Barre de progression */}
        {job.status === 'RUNNING' && (
          <div className="mb-6">
            <div className="flex justify-between text-sm text-gray-600 mb-2">
              <span>Progression de l'analyse</span>
              <span>{job.progress}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-3">
              <motion.div
                className="bg-blue-500 h-3 rounded-full"
                initial={{ width: 0 }}
                animate={{ width: `${job.progress}%` }}
                transition={{ duration: 0.5 }}
              />
            </div>
            <p className="text-xs text-gray-500 mt-1">
              Temps écoulé: {job.started_at && formatDuration(job.started_at)}
            </p>
          </div>
        )}

        {/* Informations détaillées */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
          {/* Paramètres d'analyse */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900">Paramètres d'analyse</h3>
            
            <div className="bg-gray-50 rounded-lg p-4 space-y-3">
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-600">Type d'analyse :</span>
                <span className="font-medium">{job.parameters.analysis_type}</span>
              </div>
              
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-600">Sensibilité :</span>
                <span className="font-medium">{job.parameters.detection_sensitivity}</span>
              </div>
              
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-600">Couverture nuageuse max :</span>
                <span className="font-medium">{job.parameters.cloud_coverage_max}%</span>
              </div>
              
              <div className="text-sm">
                <span className="text-gray-600">Période :</span>
                <div className="flex items-center mt-1">
                  <CalendarIcon className="w-4 h-4 mr-1 text-gray-500" />
                  <span className="font-medium">
                    {new Date(job.parameters.start_date).toLocaleDateString('fr-FR')} - {new Date(job.parameters.end_date).toLocaleDateString('fr-FR')}
                  </span>
                </div>
              </div>
              
              <div className="text-sm">
                <span className="text-gray-600">Sources satellites :</span>
                <div className="flex items-center mt-1">
                  <SatelliteIcon className="w-4 h-4 mr-1 text-gray-500" />
                  <span className="font-medium">{job.parameters.satellite_sources.join(', ')}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Informations temporelles */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900">Informations temporelles</h3>
            
            <div className="bg-gray-50 rounded-lg p-4 space-y-3">
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-600">Créée le :</span>
                <span className="font-medium">
                  {new Date(job.created_at).toLocaleString('fr-FR')}
                </span>
              </div>
              
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-600">Créée par :</span>
                <span className="font-medium">{job.created_by_name}</span>
              </div>
              
              {job.started_at && (
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">Démarrée le :</span>
                  <span className="font-medium">
                    {new Date(job.started_at).toLocaleString('fr-FR')}
                  </span>
                </div>
              )}
              
              {job.completed_at && (
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">Terminée le :</span>
                  <span className="font-medium">
                    {new Date(job.completed_at).toLocaleString('fr-FR')}
                  </span>
                </div>
              )}
              
              {job.started_at && (
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">Durée :</span>
                  <span className="font-medium">
                    {formatDuration(job.started_at, job.completed_at)}
                  </span>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Résultats */}
        {job.status === 'COMPLETED' && (
          <div className="mb-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Résultats de l'analyse</h3>
            
            {isLoadingResults ? (
              <div className="flex justify-center py-8">
                <Loading />
              </div>
            ) : results ? (
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">{results.detections_found}</div>
                    <div className="text-sm text-green-700">Détections</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">{results.total_images_processed}</div>
                    <div className="text-sm text-blue-700">Images traitées</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-orange-600">{results.alerts_generated}</div>
                    <div className="text-sm text-orange-700">Alertes générées</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-purple-600">{results.investigations_created}</div>
                    <div className="text-sm text-purple-700">Investigations créées</div>
                  </div>
                </div>
                
                {results.detection_summary && (
                  <div className="mt-4">
                    <h4 className="font-medium text-gray-900 mb-2">Répartition par type</h4>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-sm">
                      <div>Sites miniers: {results.detection_summary.mining_sites}</div>
                      <div>Pollution eau: {results.detection_summary.water_pollution}</div>
                      <div>Déforestation: {results.detection_summary.deforestation}</div>
                      <div>Perturbation sol: {results.detection_summary.soil_disturbance}</div>
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <div className="text-center py-4 text-gray-500">
                Aucun résultat disponible
              </div>
            )}
          </div>
        )}

        {/* Message d'erreur */}
        {job.status === 'FAILED' && job.error_message && (
          <div className="mb-6">
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <h4 className="font-medium text-red-900 mb-2">Erreur d'exécution</h4>
              <p className="text-sm text-red-700">{job.error_message}</p>
            </div>
          </div>
        )}

        {/* Actions */}
        <div className="flex items-center justify-end space-x-3 pt-4 border-t border-gray-200">
          <Button variant="outline" onClick={onClose}>
            Fermer
          </Button>
          
          {job.status === 'RUNNING' && (
            <Button
              variant="outline"
              onClick={() => onCancel(job.id)}
              className="text-red-600 border-red-300 hover:bg-red-50"
            >
              <StopIcon className="w-4 h-4 mr-1" />
              Annuler
            </Button>
          )}
          
          {job.status === 'COMPLETED' && job.results_url && (
            <Button
              onClick={() => onDownloadReport(job.id)}
              className="flex items-center space-x-2"
            >
              <DocumentArrowDownIcon className="w-4 h-4" />
              <span>Télécharger le rapport</span>
            </Button>
          )}
        </div>
      </div>
    </Modal>
  );
};
