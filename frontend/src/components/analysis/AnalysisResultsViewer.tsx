import React, { useState } from 'react';
import { motion } from 'framer-motion';
import {
  EyeIcon,
  DocumentArrowDownIcon,
  ChartBarIcon,
  MapPinIcon,
  CalendarIcon,
  BeakerIcon,
  CheckCircleIcon,
} from '@heroicons/react/24/outline';
import { AnalysisJob } from '../../services/analysis.service';
import { Card, Button } from '../ui';

interface AnalysisResultsViewerProps {
  jobs: AnalysisJob[];
  onViewDetails: (job: AnalysisJob) => void;
  onDownloadReport: (jobId: string) => void;
}

export const AnalysisResultsViewer: React.FC<AnalysisResultsViewerProps> = ({
  jobs,
  onViewDetails,
  onDownloadReport,
}) => {
  const [sortBy, setSortBy] = useState<'date' | 'name' | 'detections'>('date');
  const [filterType, setFilterType] = useState<string>('all');

  // Filtrage et tri des jobs
  const filteredAndSortedJobs = jobs
    .filter(job => {
      if (filterType === 'all') return true;
      return job.parameters.analysis_type === filterType;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'date':
          return new Date(b.completed_at || b.created_at).getTime() - new Date(a.completed_at || a.created_at).getTime();
        case 'name':
          return a.name.localeCompare(b.name);
        case 'detections':
          // Tri par nombre de détections (simulé pour l'instant)
          return 0;
        default:
          return 0;
      }
    });

  const getAnalysisTypeLabel = (type: string) => {
    switch (type) {
      case 'MINING_DETECTION':
        return 'Détection minière';
      case 'CHANGE_DETECTION':
        return 'Détection changements';
      case 'VEGETATION_ANALYSIS':
        return 'Analyse végétation';
      default:
        return type;
    }
  };

  const getAnalysisTypeColor = (type: string) => {
    switch (type) {
      case 'MINING_DETECTION':
        return 'bg-red-100 text-red-800';
      case 'CHANGE_DETECTION':
        return 'bg-blue-100 text-blue-800';
      case 'VEGETATION_ANALYSIS':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      {/* Filtres et tri */}
      <Card className="p-4">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
          <div className="flex items-center space-x-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Filtrer par type
              </label>
              <select
                value={filterType}
                onChange={(e) => setFilterType(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
              >
                <option value="all">Tous les types</option>
                <option value="MINING_DETECTION">Détection minière</option>
                <option value="CHANGE_DETECTION">Détection changements</option>
                <option value="VEGETATION_ANALYSIS">Analyse végétation</option>
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Trier par
              </label>
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value as any)}
                className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
              >
                <option value="date">Date</option>
                <option value="name">Nom</option>
                <option value="detections">Détections</option>
              </select>
            </div>
          </div>
          
          <div className="text-sm text-gray-600">
            {filteredAndSortedJobs.length} analyse{filteredAndSortedJobs.length > 1 ? 's' : ''} terminée{filteredAndSortedJobs.length > 1 ? 's' : ''}
          </div>
        </div>
      </Card>

      {/* Liste des résultats */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {filteredAndSortedJobs.map((job, index) => (
          <motion.div
            key={job.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
          >
            <Card className="p-6 hover:shadow-lg transition-shadow">
              {/* En-tête */}
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                    <CheckCircleIcon className="w-5 h-5 text-green-600" />
                  </div>
                  <div>
                    <h3 className="font-medium text-gray-900 line-clamp-1">{job.name}</h3>
                    <p className="text-sm text-gray-500">
                      Terminée le {job.completed_at && new Date(job.completed_at).toLocaleDateString('fr-FR')}
                    </p>
                  </div>
                </div>
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${getAnalysisTypeColor(job.parameters.analysis_type)}`}>
                  {getAnalysisTypeLabel(job.parameters.analysis_type)}
                </span>
              </div>

              {/* Informations clés */}
              <div className="space-y-3 mb-4">
                <div className="flex items-center text-sm text-gray-600">
                  <CalendarIcon className="w-4 h-4 mr-2" />
                  <span>
                    Période: {new Date(job.parameters.start_date).toLocaleDateString('fr-FR')} - {new Date(job.parameters.end_date).toLocaleDateString('fr-FR')}
                  </span>
                </div>
                
                <div className="flex items-center text-sm text-gray-600">
                  <BeakerIcon className="w-4 h-4 mr-2" />
                  <span>
                    Sensibilité: {job.parameters.detection_sensitivity} • Nuages max: {job.parameters.cloud_coverage_max}%
                  </span>
                </div>
                
                <div className="flex items-center text-sm text-gray-600">
                  <MapPinIcon className="w-4 h-4 mr-2" />
                  <span>
                    Sources: {job.parameters.satellite_sources.join(', ')}
                  </span>
                </div>
              </div>

              {/* Résultats simulés (à remplacer par vraies données) */}
              <div className="bg-gray-50 rounded-lg p-3 mb-4">
                <div className="grid grid-cols-3 gap-4 text-center">
                  <div>
                    <div className="text-lg font-bold text-blue-600">
                      {Math.floor(Math.random() * 50) + 10}
                    </div>
                    <div className="text-xs text-gray-600">Images</div>
                  </div>
                  <div>
                    <div className="text-lg font-bold text-red-600">
                      {Math.floor(Math.random() * 10) + 1}
                    </div>
                    <div className="text-xs text-gray-600">Détections</div>
                  </div>
                  <div>
                    <div className="text-lg font-bold text-orange-600">
                      {Math.floor(Math.random() * 5)}
                    </div>
                    <div className="text-xs text-gray-600">Alertes</div>
                  </div>
                </div>
              </div>

              {/* Actions */}
              <div className="flex items-center justify-between">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onViewDetails(job)}
                  className="flex items-center space-x-1"
                >
                  <EyeIcon className="w-4 h-4" />
                  <span>Détails</span>
                </Button>
                
                <div className="flex items-center space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onViewDetails(job)}
                    className="flex items-center space-x-1"
                  >
                    <ChartBarIcon className="w-4 h-4" />
                    <span>Visualiser</span>
                  </Button>
                  
                  {job.results_url && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => onDownloadReport(job.id)}
                      className="flex items-center space-x-1"
                    >
                      <DocumentArrowDownIcon className="w-4 h-4" />
                      <span>Rapport</span>
                    </Button>
                  )}
                </div>
              </div>
            </Card>
          </motion.div>
        ))}
      </div>

      {/* Message si aucun résultat */}
      {filteredAndSortedJobs.length === 0 && (
        <Card className="p-12 text-center">
          <ChartBarIcon className="w-12 h-12 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Aucun résultat trouvé
          </h3>
          <p className="text-gray-500">
            {filterType === 'all' 
              ? 'Aucune analyse n\'a encore été terminée.'
              : `Aucune analyse de type "${getAnalysisTypeLabel(filterType)}" n'a été trouvée.`
            }
          </p>
        </Card>
      )}

      {/* Statistiques globales */}
      {jobs.length > 0 && (
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Statistiques globales</h3>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">{jobs.length}</div>
              <div className="text-sm text-gray-600">Analyses terminées</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {jobs.filter(j => j.parameters.analysis_type === 'MINING_DETECTION').length}
              </div>
              <div className="text-sm text-gray-600">Détections minières</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {jobs.filter(j => j.parameters.analysis_type === 'VEGETATION_ANALYSIS').length}
              </div>
              <div className="text-sm text-gray-600">Analyses végétation</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">
                {jobs.filter(j => j.parameters.analysis_type === 'CHANGE_DETECTION').length}
              </div>
              <div className="text-sm text-gray-600">Détections changements</div>
            </div>
          </div>
        </Card>
      )}
    </div>
  );
};
