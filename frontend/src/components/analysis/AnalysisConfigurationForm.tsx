import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  CalendarIcon,
  CloudIcon,
  AdjustmentsHorizontalIcon,
  SatelliteIcon,
  BeakerIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
} from '@heroicons/react/24/outline';
import { 
  AnalysisZone, 
  AnalysisParameters, 
  SatelliteSource, 
  SpectralIndex 
} from '../../services/analysis.service';
import analysisService from '../../services/analysis.service';
import { Button, Loading } from '../ui';

interface AnalysisConfigurationFormProps {
  zone: AnalysisZone;
  satelliteSources: SatelliteSource[];
  spectralIndices: SpectralIndex[];
  onSubmit: (parameters: AnalysisParameters, name: string) => void;
  onCancel: () => void;
  isLoading?: boolean;
}

export const AnalysisConfigurationForm: React.FC<AnalysisConfigurationFormProps> = ({
  zone,
  satelliteSources,
  spectralIndices,
  onSubmit,
  onCancel,
  isLoading = false,
}) => {
  // États du formulaire
  const [analysisName, setAnalysisName] = useState(`Analyse ${zone.name} - ${new Date().toLocaleDateString('fr-FR')}`);
  const [startDate, setStartDate] = useState(() => {
    const date = new Date();
    date.setMonth(date.getMonth() - 3); // 3 mois en arrière par défaut
    return date.toISOString().split('T')[0];
  });
  const [endDate, setEndDate] = useState(() => {
    const date = new Date();
    date.setDate(date.getDate() - 1); // Hier
    return date.toISOString().split('T')[0];
  });
  const [cloudCoverage, setCloudCoverage] = useState(30);
  const [detectionSensitivity, setDetectionSensitivity] = useState<'LOW' | 'MEDIUM' | 'HIGH'>('MEDIUM');
  const [analysisType, setAnalysisType] = useState<'MINING_DETECTION' | 'CHANGE_DETECTION' | 'VEGETATION_ANALYSIS'>('MINING_DETECTION');
  const [selectedSatellites, setSelectedSatellites] = useState<string[]>(['sentinel2']);
  const [selectedIndices, setSelectedIndices] = useState<string[]>(['ndvi', 'ndwi']);
  
  // Validation
  const [validation, setValidation] = useState<{
    valid: boolean;
    errors: string[];
    warnings: string[];
    estimated_processing_time: number;
    estimated_cost: number;
  } | null>(null);
  const [isValidating, setIsValidating] = useState(false);

  // Validation automatique des paramètres
  useEffect(() => {
    const validateParams = async () => {
      if (!startDate || !endDate || selectedSatellites.length === 0) return;

      setIsValidating(true);
      try {
        const parameters: AnalysisParameters = {
          zone_id: zone.id,
          custom_zone: zone.id ? undefined : zone,
          start_date: startDate,
          end_date: endDate,
          cloud_coverage_max: cloudCoverage,
          detection_sensitivity: detectionSensitivity,
          analysis_type: analysisType,
          satellite_sources: selectedSatellites,
          spectral_indices: selectedIndices,
        };

        const result = await analysisService.validateAnalysisParameters(parameters);
        setValidation(result);
      } catch (error) {
        console.error('Erreur validation:', error);
      } finally {
        setIsValidating(false);
      }
    };

    const timeoutId = setTimeout(validateParams, 500); // Debounce
    return () => clearTimeout(timeoutId);
  }, [zone, startDate, endDate, cloudCoverage, detectionSensitivity, analysisType, selectedSatellites, selectedIndices]);

  const handleSatelliteToggle = (satelliteId: string) => {
    setSelectedSatellites(prev => 
      prev.includes(satelliteId)
        ? prev.filter(id => id !== satelliteId)
        : [...prev, satelliteId]
    );
  };

  const handleIndexToggle = (indexId: string) => {
    setSelectedIndices(prev => 
      prev.includes(indexId)
        ? prev.filter(id => id !== indexId)
        : [...prev, indexId]
    );
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validation?.valid) return;

    const parameters: AnalysisParameters = {
      zone_id: zone.id,
      custom_zone: zone.id ? undefined : zone,
      start_date: startDate,
      end_date: endDate,
      cloud_coverage_max: cloudCoverage,
      detection_sensitivity: detectionSensitivity,
      analysis_type: analysisType,
      satellite_sources: selectedSatellites,
      spectral_indices: selectedIndices,
    };

    onSubmit(parameters, analysisName);
  };

  const formatProcessingTime = (seconds: number) => {
    if (seconds < 60) return `${seconds}s`;
    if (seconds < 3600) return `${Math.round(seconds / 60)}min`;
    return `${Math.round(seconds / 3600)}h`;
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Nom de l'analyse */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Nom de l'analyse
        </label>
        <input
          type="text"
          value={analysisName}
          onChange={(e) => setAnalysisName(e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
          required
        />
      </div>

      {/* Période d'analyse */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            <CalendarIcon className="w-4 h-4 inline mr-1" />
            Date de début
          </label>
          <input
            type="date"
            value={startDate}
            onChange={(e) => setStartDate(e.target.value)}
            max={endDate}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
            required
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            <CalendarIcon className="w-4 h-4 inline mr-1" />
            Date de fin
          </label>
          <input
            type="date"
            value={endDate}
            onChange={(e) => setEndDate(e.target.value)}
            min={startDate}
            max={new Date().toISOString().split('T')[0]}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
            required
          />
        </div>
      </div>

      {/* Type d'analyse */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          <BeakerIcon className="w-4 h-4 inline mr-1" />
          Type d'analyse
        </label>
        <select
          value={analysisType}
          onChange={(e) => setAnalysisType(e.target.value as any)}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
        >
          <option value="MINING_DETECTION">Détection sites miniers</option>
          <option value="CHANGE_DETECTION">Détection changements</option>
          <option value="VEGETATION_ANALYSIS">Analyse végétation</option>
        </select>
      </div>

      {/* Paramètres avancés */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            <CloudIcon className="w-4 h-4 inline mr-1" />
            Couverture nuageuse max (%)
          </label>
          <input
            type="range"
            min="0"
            max="100"
            value={cloudCoverage}
            onChange={(e) => setCloudCoverage(Number(e.target.value))}
            className="w-full"
          />
          <div className="text-sm text-gray-600 mt-1">{cloudCoverage}%</div>
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            <AdjustmentsHorizontalIcon className="w-4 h-4 inline mr-1" />
            Sensibilité détection
          </label>
          <select
            value={detectionSensitivity}
            onChange={(e) => setDetectionSensitivity(e.target.value as any)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
          >
            <option value="LOW">Faible (moins de faux positifs)</option>
            <option value="MEDIUM">Moyenne (équilibrée)</option>
            <option value="HIGH">Élevée (plus de détections)</option>
          </select>
        </div>
      </div>

      {/* Sources satellites */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-3">
          <SatelliteIcon className="w-4 h-4 inline mr-1" />
          Sources satellites
        </label>
        <div className="space-y-2">
          {satelliteSources.map((source) => (
            <motion.div
              key={source.id}
              whileHover={{ scale: 1.02 }}
              className={`p-3 border rounded-lg cursor-pointer transition-all ${
                selectedSatellites.includes(source.id)
                  ? 'border-purple-500 bg-purple-50'
                  : 'border-gray-200 hover:border-gray-300'
              }`}
              onClick={() => handleSatelliteToggle(source.id)}
            >
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-medium text-gray-900">{source.name}</h4>
                  <p className="text-sm text-gray-600">{source.description}</p>
                  <div className="text-xs text-gray-500 mt-1">
                    Résolution: {source.resolution_meters}m • Revisite: {source.revisit_days} jours
                  </div>
                </div>
                <div className={`w-5 h-5 rounded-full border-2 ${
                  selectedSatellites.includes(source.id)
                    ? 'bg-purple-500 border-purple-500'
                    : 'border-gray-300'
                }`}>
                  {selectedSatellites.includes(source.id) && (
                    <CheckCircleIcon className="w-3 h-3 text-white m-0.5" />
                  )}
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </div>

      {/* Indices spectraux */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-3">
          Indices spectraux
        </label>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
          {spectralIndices.map((index) => (
            <motion.div
              key={index.id}
              whileHover={{ scale: 1.02 }}
              className={`p-3 border rounded-lg cursor-pointer transition-all ${
                selectedIndices.includes(index.id)
                  ? 'border-purple-500 bg-purple-50'
                  : 'border-gray-200 hover:border-gray-300'
              }`}
              onClick={() => handleIndexToggle(index.id)}
            >
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-medium text-gray-900">{index.name}</h4>
                  <p className="text-xs text-gray-600">{index.use_case}</p>
                </div>
                <div className={`w-4 h-4 rounded border ${
                  selectedIndices.includes(index.id)
                    ? 'bg-purple-500 border-purple-500'
                    : 'border-gray-300'
                }`}>
                  {selectedIndices.includes(index.id) && (
                    <CheckCircleIcon className="w-3 h-3 text-white" />
                  )}
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </div>

      {/* Validation et estimation */}
      {(isValidating || validation) && (
        <div className="bg-gray-50 rounded-lg p-4">
          <h4 className="font-medium text-gray-900 mb-2">Validation des paramètres</h4>
          
          {isValidating ? (
            <div className="flex items-center space-x-2">
              <Loading size="sm" />
              <span className="text-sm text-gray-600">Validation en cours...</span>
            </div>
          ) : validation && (
            <div className="space-y-2">
              {validation.errors.length > 0 && (
                <div className="space-y-1">
                  {validation.errors.map((error, index) => (
                    <div key={index} className="flex items-center space-x-2 text-red-600">
                      <ExclamationTriangleIcon className="w-4 h-4" />
                      <span className="text-sm">{error}</span>
                    </div>
                  ))}
                </div>
              )}
              
              {validation.warnings.length > 0 && (
                <div className="space-y-1">
                  {validation.warnings.map((warning, index) => (
                    <div key={index} className="flex items-center space-x-2 text-orange-600">
                      <ExclamationTriangleIcon className="w-4 h-4" />
                      <span className="text-sm">{warning}</span>
                    </div>
                  ))}
                </div>
              )}
              
              {validation.valid && (
                <div className="flex items-center space-x-2 text-green-600">
                  <CheckCircleIcon className="w-4 h-4" />
                  <span className="text-sm">
                    Paramètres valides • Temps estimé: {formatProcessingTime(validation.estimated_processing_time)}
                  </span>
                </div>
              )}
            </div>
          )}
        </div>
      )}

      {/* Actions */}
      <div className="flex items-center justify-end space-x-3 pt-4 border-t border-gray-200">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          disabled={isLoading}
        >
          Annuler
        </Button>
        <Button
          type="submit"
          disabled={!validation?.valid || isLoading || isValidating}
          className="flex items-center space-x-2"
        >
          {isLoading ? (
            <>
              <Loading size="sm" />
              <span>Lancement...</span>
            </>
          ) : (
            <>
              <BeakerIcon className="w-4 h-4" />
              <span>Lancer l'analyse</span>
            </>
          )}
        </Button>
      </div>
    </form>
  );
};
