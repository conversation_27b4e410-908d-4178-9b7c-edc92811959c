import React, { useState, useEffect, useRef } from 'react';
import { motion } from 'framer-motion';
import {
  PlayIcon,
  MapIcon,
  AdjustmentsHorizontalIcon,
  EyeIcon,
  EyeSlashIcon,
  ArrowPathIcon,
} from '@heroicons/react/24/outline';
import { Card, Button, Badge, Loading } from '../ui';
import { usePermissions } from '../../hooks/usePermissions';
import geeService, { SpectralMapsData } from '../../services/gee.service';
import toast from 'react-hot-toast';

interface GEEMapProps {
  onAnalysisLaunch?: (bounds: google.maps.LatLngBounds) => void;
  onDetectionSelect?: (detection: any) => void;
  detections?: any[];
  isAnalyzing?: boolean;
}

interface MapLayers {
  ndvi: boolean;
  ndwi: boolean;
  ndti: boolean;
  detections: boolean;
}

export const GEEMap: React.FC<GEEMapProps> = ({
  onAnalysisLaunch,
  onDetectionSelect,
  detections = [],
  isAnalyzing = false,
}) => {
  const mapRef = useRef<HTMLDivElement>(null);
  const mapInstanceRef = useRef<google.maps.Map | null>(null);
  const drawingManagerRef = useRef<google.maps.drawing.DrawingManager | null>(null);
  const selectedAreaRef = useRef<google.maps.Rectangle | null>(null);
  
  const permissions = usePermissions();
  
  const [isMapLoaded, setIsMapLoaded] = useState(false);
  const [selectedBounds, setSelectedBounds] = useState<google.maps.LatLngBounds | null>(null);
  const [spectralData, setSpectralData] = useState<SpectralMapsData | null>(null);
  const [isLoadingSpectral, setIsLoadingSpectral] = useState(false);
  const [activeLayers, setActiveLayers] = useState<MapLayers>({
    ndvi: false,
    ndwi: false,
    ndti: false,
    detections: true,
  });

  // Initialiser Google Maps
  useEffect(() => {
    const initMap = () => {
      if (!mapRef.current || !window.google) return;

      // Créer la carte centrée sur Bondoukou
      const map = new google.maps.Map(mapRef.current, {
        center: { lat: 8.0, lng: -3.0 }, // Bondoukou, Côte d'Ivoire
        zoom: 10,
        mapTypeId: google.maps.MapTypeId.SATELLITE,
        mapTypeControl: true,
        streetViewControl: false,
        fullscreenControl: true,
        zoomControl: true,
      });

      mapInstanceRef.current = map;

      // Initialiser le gestionnaire de dessin pour sélection de zone
      if (permissions.canLaunchAnalysis()) {
        const drawingManager = new google.maps.drawing.DrawingManager({
          drawingMode: null,
          drawingControl: true,
          drawingControlOptions: {
            position: google.maps.ControlPosition.TOP_CENTER,
            drawingModes: [google.maps.drawing.OverlayType.RECTANGLE],
          },
          rectangleOptions: {
            fillColor: '#FF0000',
            fillOpacity: 0.2,
            strokeWeight: 2,
            strokeColor: '#FF0000',
            clickable: false,
            editable: true,
            zIndex: 1,
          },
        });

        drawingManager.setMap(map);
        drawingManagerRef.current = drawingManager;

        // Écouter la création de rectangles
        drawingManager.addListener('rectanglecomplete', (rectangle: google.maps.Rectangle) => {
          // Supprimer l'ancien rectangle s'il existe
          if (selectedAreaRef.current) {
            selectedAreaRef.current.setMap(null);
          }

          selectedAreaRef.current = rectangle;
          const bounds = rectangle.getBounds();
          if (bounds) {
            setSelectedBounds(bounds);
          }

          // Désactiver le mode dessin
          drawingManager.setDrawingMode(null);
        });
      }

      setIsMapLoaded(true);
    };

    // Charger Google Maps API si pas déjà chargé
    if (!window.google) {
      const script = document.createElement('script');
      script.src = `https://maps.googleapis.com/maps/api/js?key=${import.meta.env.VITE_GOOGLE_MAPS_API_KEY}&libraries=drawing`;
      script.async = true;
      script.onload = initMap;
      document.head.appendChild(script);
    } else {
      initMap();
    }

    return () => {
      // Cleanup
      if (selectedAreaRef.current) {
        selectedAreaRef.current.setMap(null);
      }
      if (drawingManagerRef.current) {
        drawingManagerRef.current.setMap(null);
      }
    };
  }, [permissions]);

  // Afficher les détections sur la carte
  useEffect(() => {
    if (!mapInstanceRef.current || !activeLayers.detections) return;

    // Supprimer les anciens marqueurs
    // TODO: Gérer les marqueurs existants

    // Ajouter les nouveaux marqueurs
    detections.forEach((detection) => {
      const marker = new google.maps.Marker({
        position: { lat: detection.latitude, lng: detection.longitude },
        map: mapInstanceRef.current,
        title: `Détection ${detection.id}`,
        icon: {
          url: getDetectionIcon(detection.detection_type, detection.confidence_score),
          scaledSize: new google.maps.Size(24, 24),
        },
      });

      marker.addListener('click', () => {
        onDetectionSelect?.(detection);
      });
    });
  }, [detections, activeLayers.detections, onDetectionSelect]);

  const getDetectionIcon = (type: string, confidence: number) => {
    const color = confidence >= 0.8 ? 'red' : confidence >= 0.5 ? 'orange' : 'yellow';
    return `https://maps.google.com/mapfiles/ms/icons/${color}-dot.png`;
  };

  const handleLaunchAnalysis = () => {
    if (!selectedBounds) {
      toast.error('Veuillez sélectionner une zone sur la carte');
      return;
    }

    onAnalysisLaunch?.(selectedBounds);
  };

  const handleClearSelection = () => {
    if (selectedAreaRef.current) {
      selectedAreaRef.current.setMap(null);
      selectedAreaRef.current = null;
    }
    setSelectedBounds(null);
  };

  const toggleLayer = (layer: keyof MapLayers) => {
    setActiveLayers(prev => ({
      ...prev,
      [layer]: !prev[layer],
    }));
  };

  const loadSpectralLayers = async () => {
    if (!spectralData) {
      toast.error('Aucune donnée spectrale disponible');
      return;
    }

    setIsLoadingSpectral(true);
    try {
      const map = mapInstanceRef.current;
      if (!map) return;

      // Ajouter les couches spectrales comme overlays
      if (activeLayers.ndvi && spectralData.spectral_maps.ndvi_map_url) {
        // TODO: Implémenter l'ajout de couches GEE
        toast.info('Couche NDVI ajoutée');
      }

      if (activeLayers.ndwi && spectralData.spectral_maps.ndwi_map_url) {
        toast.info('Couche NDWI ajoutée');
      }

      if (activeLayers.ndti && spectralData.spectral_maps.ndti_map_url) {
        toast.info('Couche NDTI ajoutée');
      }
    } catch (error) {
      toast.error('Erreur lors du chargement des couches spectrales');
    } finally {
      setIsLoadingSpectral(false);
    }
  };

  return (
    <Card className="h-full flex flex-col">
      {/* Contrôles de la carte */}
      <div className="p-4 border-b border-slate-200">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-2">
            <MapIcon className="w-5 h-5 text-slate-600" />
            <h3 className="font-semibold text-slate-900">Carte Google Earth Engine</h3>
            {selectedBounds && (
              <Badge variant="blue" className="text-xs">
                Zone sélectionnée
              </Badge>
            )}
          </div>

          {permissions.canLaunchAnalysis() && (
            <div className="flex items-center space-x-2">
              {selectedBounds && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleClearSelection}
                  className="text-slate-600"
                >
                  Effacer sélection
                </Button>
              )}
              <Button
                variant="primary"
                size="sm"
                onClick={handleLaunchAnalysis}
                disabled={!selectedBounds || isAnalyzing}
                loading={isAnalyzing}
                className="flex items-center space-x-1"
              >
                <PlayIcon className="w-4 h-4" />
                <span>Lancer Analyse</span>
              </Button>
            </div>
          )}
        </div>

        {/* Contrôles des couches */}
        <div className="flex items-center space-x-4">
          <span className="text-sm font-medium text-slate-700">Couches :</span>
          
          <button
            onClick={() => toggleLayer('detections')}
            className={`flex items-center space-x-1 px-2 py-1 rounded text-xs transition-colors ${
              activeLayers.detections
                ? 'bg-blue-100 text-blue-800'
                : 'bg-slate-100 text-slate-600 hover:bg-slate-200'
            }`}
          >
            {activeLayers.detections ? <EyeIcon className="w-3 h-3" /> : <EyeSlashIcon className="w-3 h-3" />}
            <span>Détections</span>
          </button>

          <button
            onClick={() => toggleLayer('ndvi')}
            className={`flex items-center space-x-1 px-2 py-1 rounded text-xs transition-colors ${
              activeLayers.ndvi
                ? 'bg-green-100 text-green-800'
                : 'bg-slate-100 text-slate-600 hover:bg-slate-200'
            }`}
          >
            {activeLayers.ndvi ? <EyeIcon className="w-3 h-3" /> : <EyeSlashIcon className="w-3 h-3" />}
            <span>NDVI</span>
          </button>

          <button
            onClick={() => toggleLayer('ndwi')}
            className={`flex items-center space-x-1 px-2 py-1 rounded text-xs transition-colors ${
              activeLayers.ndwi
                ? 'bg-blue-100 text-blue-800'
                : 'bg-slate-100 text-slate-600 hover:bg-slate-200'
            }`}
          >
            {activeLayers.ndwi ? <EyeIcon className="w-3 h-3" /> : <EyeSlashIcon className="w-3 h-3" />}
            <span>NDWI</span>
          </button>

          <button
            onClick={() => toggleLayer('ndti')}
            className={`flex items-center space-x-1 px-2 py-1 rounded text-xs transition-colors ${
              activeLayers.ndti
                ? 'bg-orange-100 text-orange-800'
                : 'bg-slate-100 text-slate-600 hover:bg-slate-200'
            }`}
          >
            {activeLayers.ndti ? <EyeIcon className="w-3 h-3" /> : <EyeSlashIcon className="w-3 h-3" />}
            <span>NDTI</span>
          </button>

          {spectralData && (
            <Button
              variant="ghost"
              size="sm"
              onClick={loadSpectralLayers}
              loading={isLoadingSpectral}
              className="flex items-center space-x-1"
            >
              <ArrowPathIcon className="w-3 h-3" />
              <span>Actualiser couches</span>
            </Button>
          )}
        </div>
      </div>

      {/* Carte */}
      <div className="flex-1 relative">
        <div ref={mapRef} className="w-full h-full" />
        
        {!isMapLoaded && (
          <div className="absolute inset-0 flex items-center justify-center bg-slate-100">
            <Loading />
          </div>
        )}

        {/* Instructions */}
        {isMapLoaded && permissions.canLaunchAnalysis() && !selectedBounds && (
          <div className="absolute top-4 left-4 bg-white rounded-lg shadow-lg p-3 border max-w-xs">
            <p className="text-sm text-slate-600">
              <strong>Instructions :</strong><br />
              1. Utilisez l'outil rectangle pour sélectionner une zone<br />
              2. Cliquez sur "Lancer Analyse" pour démarrer
            </p>
          </div>
        )}

        {/* Informations de la zone sélectionnée */}
        {selectedBounds && (
          <div className="absolute bottom-4 left-4 bg-white rounded-lg shadow-lg p-3 border">
            <h4 className="text-sm font-semibold text-slate-900 mb-2">Zone sélectionnée</h4>
            <div className="text-xs text-slate-600 space-y-1">
              <div>Nord: {selectedBounds.getNorthEast().lat().toFixed(6)}</div>
              <div>Sud: {selectedBounds.getSouthWest().lat().toFixed(6)}</div>
              <div>Est: {selectedBounds.getNorthEast().lng().toFixed(6)}</div>
              <div>Ouest: {selectedBounds.getSouthWest().lng().toFixed(6)}</div>
            </div>
          </div>
        )}
      </div>

      {/* Légende */}
      <div className="p-3 border-t border-slate-200 bg-slate-50">
        <div className="flex items-center justify-between text-xs text-slate-500">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-1">
              <div className="w-2 h-2 bg-red-500 rounded-full"></div>
              <span>Haute confiance</span>
            </div>
            <div className="flex items-center space-x-1">
              <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
              <span>Moyenne confiance</span>
            </div>
            <div className="flex items-center space-x-1">
              <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
              <span>Faible confiance</span>
            </div>
          </div>
          <span>{detections.length} détection(s) affichée(s)</span>
        </div>
      </div>
    </Card>
  );
};
