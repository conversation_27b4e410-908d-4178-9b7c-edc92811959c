import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  BugAntIcon,
  XMarkIcon,
  UserIcon,
  ShieldCheckIcon,
  KeyIcon,
  ClockIcon,
  InformationCircleIcon,
} from '@heroicons/react/24/outline';
import { useAuth } from '../contexts/AuthContext';
import { usePermissions } from '../hooks/usePermissions';
import { Card } from './ui';

export const DebugPanel: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const { user, isAuthenticated } = useAuth();
  const permissions = usePermissions();

  // Ne pas afficher en production
  if (import.meta.env.PROD) {
    return null;
  }

  const permissionChecks = [
    { name: 'canViewStats', value: permissions.canViewStats() },
    { name: 'canLaunchAnalysis', value: permissions.canLaunchAnalysis() },
    { name: 'canManageInvestigations', value: permissions.canManageInvestigations() },
    { name: 'canViewFinancialRisks', value: permissions.canViewFinancialRisks() },
    { name: 'isAdmin', value: permissions.isAdmin() },
    { name: 'isAdministrateur', value: permissions.isAdministrateur() },
    { name: 'isResponsableRegional', value: permissions.isResponsableRegional() },
    { name: 'isAgentAnalyste', value: permissions.isAgentAnalyste() },
    { name: 'isAgentTechnique', value: permissions.isAgentTechnique() },
    { name: 'isAgentTerrain', value: permissions.isAgentTerrain() },
  ];

  return (
    <>
      {/* Bouton de debug flottant */}
      <motion.button
        className="fixed bottom-4 right-4 z-50 w-12 h-12 bg-purple-600 text-white rounded-full shadow-lg hover:bg-purple-700 transition-colors flex items-center justify-center"
        onClick={() => setIsOpen(true)}
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.9 }}
        title="Panneau de debug (dev only)"
      >
        <BugAntIcon className="w-6 h-6" />
      </motion.button>

      {/* Panel de debug */}
      <AnimatePresence>
        {isOpen && (
          <>
            {/* Overlay */}
            <div
              className="fixed inset-0 bg-black/50 z-50"
              onClick={() => setIsOpen(false)}
            />
            
            {/* Panel */}
            <motion.div
              initial={{ opacity: 0, scale: 0.9, x: 100 }}
              animate={{ opacity: 1, scale: 1, x: 0 }}
              exit={{ opacity: 0, scale: 0.9, x: 100 }}
              className="fixed right-4 top-4 bottom-4 w-96 bg-white rounded-xl shadow-2xl z-50 overflow-hidden"
            >
              {/* En-tête */}
              <div className="bg-purple-600 text-white p-4 flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <BugAntIcon className="w-5 h-5" />
                  <h3 className="font-semibold">Debug Panel</h3>
                </div>
                <button
                  onClick={() => setIsOpen(false)}
                  className="p-1 hover:bg-purple-700 rounded"
                >
                  <XMarkIcon className="w-4 h-4" />
                </button>
              </div>

              {/* Contenu */}
              <div className="p-4 h-full overflow-y-auto">
                {/* Statut d'authentification */}
                <Card className="mb-4 p-3">
                  <h4 className="font-semibold text-slate-900 mb-2 flex items-center">
                    <KeyIcon className="w-4 h-4 mr-2 text-green-500" />
                    Authentification
                  </h4>
                  <div className="space-y-1 text-sm">
                    <div className="flex justify-between">
                      <span>Connecté:</span>
                      <span className={isAuthenticated ? 'text-green-600' : 'text-red-600'}>
                        {isAuthenticated ? '✅ Oui' : '❌ Non'}
                      </span>
                    </div>
                    {user && (
                      <>
                        <div className="flex justify-between">
                          <span>ID:</span>
                          <span className="font-mono">{user.id}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Email:</span>
                          <span className="font-mono text-xs">{user.email}</span>
                        </div>
                      </>
                    )}
                  </div>
                </Card>

                {/* Informations utilisateur */}
                {user && (
                  <Card className="mb-4 p-3">
                    <h4 className="font-semibold text-slate-900 mb-2 flex items-center">
                      <UserIcon className="w-4 h-4 mr-2 text-blue-500" />
                      Utilisateur
                    </h4>
                    <div className="space-y-1 text-sm">
                      <div className="flex justify-between">
                        <span>Nom:</span>
                        <span>{user.full_name}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Fonction:</span>
                        <span>{user.job_title || 'Non défini'}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Institution:</span>
                        <span>{user.institution || 'Non défini'}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Région:</span>
                        <span>{user.authorized_region}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Rôle principal:</span>
                        <span className="font-semibold text-purple-600">
                          {user.primary_authority}
                        </span>
                      </div>
                    </div>
                  </Card>
                )}

                {/* Autorités */}
                {user?.authorities && (
                  <Card className="mb-4 p-3">
                    <h4 className="font-semibold text-slate-900 mb-2 flex items-center">
                      <ShieldCheckIcon className="w-4 h-4 mr-2 text-orange-500" />
                      Autorités
                    </h4>
                    <div className="space-y-1">
                      {user.authorities.map((authority, index) => (
                        <div
                          key={index}
                          className={`flex items-center justify-between text-sm p-2 rounded ${
                            authority.name === user.primary_authority
                              ? 'bg-purple-50 border border-purple-200'
                              : 'bg-slate-50'
                          }`}
                        >
                          <span>{authority.name}</span>
                          <div className="flex items-center space-x-2">
                            <span className={`w-2 h-2 rounded-full ${
                              authority.status ? 'bg-green-500' : 'bg-red-500'
                            }`} />
                            {authority.name === user.primary_authority && (
                              <span className="text-xs bg-purple-100 text-purple-800 px-1 rounded">
                                Principal
                              </span>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  </Card>
                )}

                {/* Permissions */}
                <Card className="mb-4 p-3">
                  <h4 className="font-semibold text-slate-900 mb-2 flex items-center">
                    <ShieldCheckIcon className="w-4 h-4 mr-2 text-red-500" />
                    Permissions
                  </h4>
                  <div className="space-y-1">
                    {permissionChecks.map((check) => (
                      <div
                        key={check.name}
                        className="flex items-center justify-between text-sm"
                      >
                        <span className="font-mono text-xs">{check.name}</span>
                        <span className={check.value ? 'text-green-600' : 'text-red-600'}>
                          {check.value ? '✅' : '❌'}
                        </span>
                      </div>
                    ))}
                  </div>
                </Card>

                {/* Informations techniques */}
                <Card className="mb-4 p-3">
                  <h4 className="font-semibold text-slate-900 mb-2 flex items-center">
                    <InformationCircleIcon className="w-4 h-4 mr-2 text-slate-500" />
                    Technique
                  </h4>
                  <div className="space-y-1 text-sm">
                    <div className="flex justify-between">
                      <span>Environnement:</span>
                      <span className="font-mono">
                        {import.meta.env.DEV ? 'Development' : 'Production'}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span>API URL:</span>
                      <span className="font-mono text-xs">
                        {import.meta.env.VITE_API_URL || 'http://localhost:8000/api'}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span>Timestamp:</span>
                      <span className="font-mono text-xs">
                        {new Date().toLocaleTimeString()}
                      </span>
                    </div>
                  </div>
                </Card>

                {/* Actions de test */}
                <Card className="p-3">
                  <h4 className="font-semibold text-slate-900 mb-2 flex items-center">
                    <ClockIcon className="w-4 h-4 mr-2 text-yellow-500" />
                    Actions de Test
                  </h4>
                  <div className="space-y-2">
                    <button
                      onClick={() => {
                        console.log('User:', user);
                        console.log('Permissions:', permissions);
                      }}
                      className="w-full text-left text-sm bg-slate-100 hover:bg-slate-200 p-2 rounded transition-colors"
                    >
                      📝 Log dans la console
                    </button>
                    <button
                      onClick={() => {
                        localStorage.clear();
                        window.location.reload();
                      }}
                      className="w-full text-left text-sm bg-red-100 hover:bg-red-200 text-red-800 p-2 rounded transition-colors"
                    >
                      🗑️ Clear localStorage
                    </button>
                  </div>
                </Card>
              </div>
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </>
  );
};
