import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { Loading } from './ui/Loading';
import { motion } from 'framer-motion';
import { ExclamationTriangleIcon } from '@heroicons/react/24/outline';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredRoles?: string[];
  requiredPermissions?: string[];
  fallback?: React.ReactNode;
  redirectTo?: string;
}

export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requiredRoles,
  requiredPermissions,
  fallback,
  redirectTo = '/dashboard',
}) => {
  const {
    user,
    loading,
    isAuthenticated,
    hasRole,
    hasPermission,
    canViewStats,
    canManageInvestigations,
    isResponsableRegional,
    isAgentTerrain
  } = useAuth();
  const location = useLocation();

  // Loading state
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-50 to-slate-100">
        <Loading />
      </div>
    );
  }

  // Non authentifié
  if (!isAuthenticated) {
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  // Vérification des rôles requis
  if (requiredRoles && requiredRoles.length > 0) {
    const hasRequiredRole = requiredRoles.some(role => hasRole(role));
    if (!hasRequiredRole) {
      return fallback || <UnauthorizedAccess requiredRoles={requiredRoles} />;
    }
  }

  // Vérification des permissions requises
  if (requiredPermissions && requiredPermissions.length > 0) {
    const hasRequiredPermission = requiredPermissions.some(permission => hasPermission(permission));
    if (!hasRequiredPermission) {
      return fallback || <UnauthorizedAccess requiredPermissions={requiredPermissions} />;
    }
  }

  // Vérifications spécifiques par route
  const currentPath = location.pathname;

  // Protection route /dashboard et /stats
  if ((currentPath === '/dashboard' || currentPath === '/stats') && !canViewStats()) {
    return <Navigate to="/alerts" replace />; // Redirection vers alertes pour agents terrain
  }

  // Protection route /investigations
  if (currentPath === '/investigations' && !canManageInvestigations() && !isAgentTerrain()) {
    return <Navigate to={redirectTo} replace />;
  }

  // Protection route /detections (Responsables uniquement)
  if (currentPath.startsWith('/detections') && !isResponsableRegional()) {
    return <Navigate to={redirectTo} replace />;
  }

  return <>{children}</>;
};

// Composant d'accès non autorisé
const UnauthorizedAccess: React.FC<{
  requiredRoles?: string[];
  requiredPermissions?: string[];
}> = ({ requiredRoles, requiredPermissions }) => {
  const { user } = useAuth();

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-50 to-slate-100">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="max-w-md w-full mx-auto p-8"
      >
        <div className="bg-white rounded-2xl shadow-xl p-8 text-center">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <ExclamationTriangleIcon className="w-8 h-8 text-red-600" />
          </div>

          <h2 className="text-2xl font-bold text-gray-900 mb-4">
            Accès non autorisé
          </h2>

          <p className="text-gray-600 mb-6">
            Vous n'avez pas les permissions nécessaires pour accéder à cette page.
          </p>

          <div className="bg-gray-50 rounded-lg p-4 mb-6">
            <p className="text-sm text-gray-700 mb-2">
              <strong>Votre rôle :</strong> {user?.primary_authority}
            </p>
            {requiredRoles && (
              <p className="text-sm text-gray-700 mb-2">
                <strong>Rôles requis :</strong> {requiredRoles.join(', ')}
              </p>
            )}
            {requiredPermissions && (
              <p className="text-sm text-gray-700">
                <strong>Permissions requises :</strong> {requiredPermissions.join(', ')}
              </p>
            )}
          </div>

          <button
            onClick={() => window.history.back()}
            className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors"
          >
            Retour
          </button>
        </div>
      </motion.div>
    </div>
  );
};