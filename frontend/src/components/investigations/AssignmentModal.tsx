import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { XMarkIcon, UserPlusIcon, ExclamationTriangleIcon } from '@heroicons/react/24/outline';
import { Investigation, Agent } from '../../services/investigation.service';
import { Modal, Button, Loading } from '../ui';

interface AssignmentModalProps {
  isOpen: boolean;
  onClose: () => void;
  investigation: Investigation | null;
  agents: Agent[];
  onAssign: (agentId: number, priority: string, notes: string) => void;
  isLoading?: boolean;
}

export const AssignmentModal: React.FC<AssignmentModalProps> = ({
  isOpen,
  onClose,
  investigation,
  agents,
  onAssign,
  isLoading = false,
}) => {
  const [selectedAgent, setSelectedAgent] = useState<number | null>(null);
  const [priority, setPriority] = useState<string>('MEDIUM');
  const [notes, setNotes] = useState<string>('');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (selectedAgent) {
      onAssign(selectedAgent, priority, notes);
    }
  };

  const handleClose = () => {
    setSelectedAgent(null);
    setPriority('MEDIUM');
    setNotes('');
    onClose();
  };

  if (!isOpen || !investigation) return null;

  const getWorkloadColor = (workload: number) => {
    if (workload >= 8) return 'text-red-600 bg-red-100';
    if (workload >= 5) return 'text-orange-600 bg-orange-100';
    return 'text-green-600 bg-green-100';
  };

  const getWorkloadLabel = (workload: number) => {
    if (workload >= 8) return 'Surchargé';
    if (workload >= 5) return 'Occupé';
    return 'Disponible';
  };

  return (
    <Modal isOpen={isOpen} onClose={handleClose} size="lg">
      <div className="p-6">
        {/* En-tête */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
              <UserPlusIcon className="w-5 h-5 text-blue-600" />
            </div>
            <div>
              <h2 className="text-xl font-bold text-gray-900">
                Assigner Investigation #{investigation.id}
              </h2>
              <p className="text-sm text-gray-600">
                {investigation.detection_info?.type} - {investigation.detection_info?.area_hectares?.toFixed(2)} ha
              </p>
            </div>
          </div>
          <button
            onClick={handleClose}
            className="p-2 hover:bg-gray-100 rounded-full transition-colors"
          >
            <XMarkIcon className="w-5 h-5 text-gray-500" />
          </button>
        </div>

        {/* Informations investigation */}
        <div className="bg-gray-50 rounded-lg p-4 mb-6">
          <h3 className="font-medium text-gray-900 mb-2">Détails de l'investigation</h3>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-gray-600">Coordonnées :</span>
              <span className="ml-2 font-medium">{investigation.target_coordinates}</span>
            </div>
            <div>
              <span className="text-gray-600">Score confiance :</span>
              <span className="ml-2 font-medium">
                {Math.round((investigation.detection_info?.confidence_score || 0) * 100)}%
              </span>
            </div>
          </div>
          {investigation.access_instructions && (
            <div className="mt-2">
              <span className="text-gray-600">Instructions d'accès :</span>
              <p className="mt-1 text-sm text-gray-800">{investigation.access_instructions}</p>
            </div>
          )}
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Sélection agent */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-3">
              Sélectionner un agent terrain
            </label>
            <div className="space-y-2 max-h-64 overflow-y-auto">
              {agents.map((agent) => (
                <motion.div
                  key={agent.id}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  className={`p-4 border rounded-lg cursor-pointer transition-all ${
                    selectedAgent === agent.id
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                  onClick={() => setSelectedAgent(agent.id)}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center">
                        <span className="text-sm font-medium text-gray-700">
                          {agent.full_name.split(' ').map(n => n[0]).join('')}
                        </span>
                      </div>
                      <div>
                        <h4 className="font-medium text-gray-900">{agent.full_name}</h4>
                        <p className="text-sm text-gray-600">{agent.identifier}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getWorkloadColor(agent.total_workload)}`}>
                        {getWorkloadLabel(agent.total_workload)}
                      </span>
                      <p className="text-xs text-gray-500 mt-1">
                        {agent.total_workload} investigation{agent.total_workload > 1 ? 's' : ''}
                      </p>
                    </div>
                  </div>
                  {agent.total_workload >= 8 && (
                    <div className="mt-2 flex items-center space-x-1 text-xs text-red-600">
                      <ExclamationTriangleIcon className="w-3 h-3" />
                      <span>Agent surchargé - Assignation déconseillée</span>
                    </div>
                  )}
                </motion.div>
              ))}
            </div>
          </div>

          {/* Priorité */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Priorité
            </label>
            <select
              value={priority}
              onChange={(e) => setPriority(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="LOW">Faible</option>
              <option value="MEDIUM">Moyenne</option>
              <option value="HIGH">Élevée</option>
              <option value="URGENT">Urgente</option>
            </select>
          </div>

          {/* Notes */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Notes pour l'agent (optionnel)
            </label>
            <textarea
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Instructions spéciales, équipement nécessaire, etc."
            />
          </div>

          {/* Actions */}
          <div className="flex items-center justify-end space-x-3 pt-4 border-t border-gray-200">
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={isLoading}
            >
              Annuler
            </Button>
            <Button
              type="submit"
              disabled={!selectedAgent || isLoading}
              className="flex items-center space-x-2"
            >
              {isLoading ? (
                <>
                  <Loading size="sm" />
                  <span>Assignation...</span>
                </>
              ) : (
                <>
                  <UserPlusIcon className="w-4 h-4" />
                  <span>Assigner</span>
                </>
              )}
            </Button>
          </div>
        </form>
      </div>
    </Modal>
  );
};
