import React from 'react';
import { motion } from 'framer-motion';
import { Card } from '../ui';

interface SpectralMapSimpleProps {
  title: string;
  mapUrl: string;
  bounds: {
    north: number;
    south: number;
    east: number;
    west: number;
  };
  colorScheme: 'vegetation' | 'water' | 'soil';
  className?: string;
}

const colorSchemes = {
  vegetation: {
    gradient: 'from-red-500 via-yellow-500 to-green-500',
    description: 'Rouge: Faible végétation, Vert: Végétation dense',
    icon: '🌱',
    color: 'bg-green-500'
  },
  water: {
    gradient: 'from-white to-blue-500',
    description: 'Blanc: Sec, Bleu: Présence d\'eau',
    icon: '💧',
    color: 'bg-blue-500'
  },
  soil: {
    gradient: 'from-blue-500 via-white to-red-500',
    description: 'Bleu: Sol naturel, Rouge: Sol perturbé',
    icon: '🏔️',
    color: 'bg-orange-500'
  }
};

export const SpectralMapSimple: React.FC<SpectralMapSimpleProps> = ({
  title,
  mapUrl,
  bounds,
  colorScheme,
  className = ''
}) => {
  const scheme = colorSchemes[colorScheme];

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className={className}
    >
      <Card className="overflow-hidden">
        <div className="p-4 border-b border-slate-200">
          <h3 className="text-lg font-semibold text-slate-900 mb-2 flex items-center">
            <span className="text-2xl mr-2">{scheme.icon}</span>
            {title}
          </h3>
          
          {/* Légende des couleurs */}
          <div className="flex items-center space-x-3">
            <div className={`h-4 w-24 bg-gradient-to-r ${scheme.gradient} rounded-full`}></div>
            <span className="text-xs text-slate-600">{scheme.description}</span>
          </div>
        </div>
        
        {/* Zone de carte simulée */}
        <div className="relative">
          <div className="h-64 w-full bg-gradient-to-br from-slate-100 to-slate-200 flex items-center justify-center">
            <div className="text-center">
              <div className={`w-20 h-20 ${scheme.color} rounded-full mx-auto mb-4 flex items-center justify-center shadow-lg`}>
                <span className="text-3xl text-white">{scheme.icon}</span>
              </div>
              
              <h4 className="text-lg font-semibold text-slate-900 mb-2">
                Carte {title}
              </h4>
              
              <div className="space-y-2 text-sm text-slate-600">
                <div className="flex items-center justify-center space-x-4">
                  <span>📍 Bondoukou, Côte d'Ivoire</span>
                </div>
                <div className="flex items-center justify-center space-x-4">
                  <span>🛰️ Sentinel-2 (10m)</span>
                </div>
                <div className="flex items-center justify-center space-x-4">
                  <span>🗺️ {bounds.north.toFixed(2)}°N, {Math.abs(bounds.west).toFixed(2)}°W</span>
                </div>
              </div>
              
              {/* Simulation de données */}
              <div className="mt-4 p-3 bg-white rounded-lg shadow-sm border">
                <div className="text-xs text-slate-500 mb-2">Données simulées</div>
                <div className="grid grid-cols-2 gap-2 text-xs">
                  <div>
                    <span className="font-medium">Zone couverte:</span>
                    <br />
                    <span className="text-slate-600">~12,000 km²</span>
                  </div>
                  <div>
                    <span className="font-medium">Résolution:</span>
                    <br />
                    <span className="text-slate-600">10 mètres</span>
                  </div>
                </div>
              </div>
              
              {/* Indicateur de statut */}
              <div className="mt-3 flex items-center justify-center space-x-2">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                <span className="text-xs text-slate-500">
                  {mapUrl.includes('mock-') ? 'Mode développement' : 'Données GEE'}
                </span>
              </div>
            </div>
          </div>
        </div>
        
        {/* Informations techniques */}
        <div className="p-4 bg-slate-50 border-t">
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="font-medium text-slate-700">Collection:</span>
              <div className="text-slate-600">COPERNICUS/S2_SR</div>
            </div>
            <div>
              <span className="font-medium text-slate-700">Bandes utilisées:</span>
              <div className="text-slate-600">
                {colorScheme === 'vegetation' && 'B4 (Red), B8 (NIR)'}
                {colorScheme === 'water' && 'B3 (Green), B8 (NIR)'}
                {colorScheme === 'soil' && 'B11 (SWIR1), B12 (SWIR2)'}
              </div>
            </div>
          </div>
          
          {/* Formule de l'indice */}
          <div className="mt-3 p-2 bg-white rounded border">
            <span className="font-medium text-slate-700 text-xs">Formule:</span>
            <div className="text-xs text-slate-600 font-mono mt-1">
              {colorScheme === 'vegetation' && '(NIR - Red) / (NIR + Red)'}
              {colorScheme === 'water' && '(Green - NIR) / (Green + NIR)'}
              {colorScheme === 'soil' && '(SWIR1 - SWIR2) / (SWIR1 + SWIR2)'}
            </div>
          </div>
        </div>
      </Card>
    </motion.div>
  );
};
