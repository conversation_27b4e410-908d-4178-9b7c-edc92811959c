import React, { useEffect, useRef } from 'react';
import { motion } from 'framer-motion';
import L from 'leaflet';
import 'leaflet/dist/leaflet.css';
import { Card } from '../ui';

interface SpectralMapProps {
  title: string;
  mapUrl: string;
  bounds: {
    north: number;
    south: number;
    east: number;
    west: number;
  };
  colorScheme: 'vegetation' | 'water' | 'soil';
  className?: string;
}

const colorSchemes = {
  vegetation: {
    gradient: 'from-red-500 via-yellow-500 to-green-500',
    description: 'Rouge: Faible végétation, Vert: Végétation dense'
  },
  water: {
    gradient: 'from-white to-blue-500',
    description: 'Blanc: Sec, Bleu: Présence d\'eau'
  },
  soil: {
    gradient: 'from-blue-500 via-white to-red-500',
    description: 'Bleu: Sol naturel, Rouge: Sol perturbé'
  }
};

export const SpectralMap: React.FC<SpectralMapProps> = ({
  title,
  mapUrl,
  bounds,
  colorScheme,
  className = ''
}) => {
  const mapRef = useRef<HTMLDivElement>(null);
  const mapInstanceRef = useRef<L.Map | null>(null);
  const containerIdRef = useRef<string>(`map-${Math.random().toString(36).substr(2, 9)}`);

  useEffect(() => {
    if (!mapRef.current) return;

    // Nettoyage complet de la carte existante
    if (mapInstanceRef.current) {
      try {
        mapInstanceRef.current.remove();
        mapInstanceRef.current = null;
      } catch (error) {
        console.warn('Erreur lors du nettoyage de la carte:', error);
      }
    }

    // Vider le conteneur
    if (mapRef.current) {
      mapRef.current.innerHTML = '';
    }

    // Ne créer la carte que si on a une URL
    if (!mapUrl) return;

    try {
      // Création de la nouvelle carte avec un ID unique
      const map = L.map(mapRef.current, {
        center: [(bounds.north + bounds.south) / 2, (bounds.east + bounds.west) / 2],
        zoom: 12,
        zoomControl: true,
        attributionControl: false
      });

      // Ajout de la couche de base
      L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        opacity: 0.3
      }).addTo(map);

      // Ajout de la couche spectrale (simulée pour le moment)
      // Note: Les URLs simulées ne fonctionneront pas, on affiche juste la carte de base
      if (mapUrl.includes('mock-')) {
        // Pour les cartes simulées, on ajoute juste un marqueur indicatif
        L.marker([(bounds.north + bounds.south) / 2, (bounds.east + bounds.west) / 2])
          .addTo(map)
          .bindPopup(`Carte ${title} (simulée)`);
      } else {
        // Pour les vraies cartes GEE
        L.tileLayer(mapUrl, {
          opacity: 0.8,
          attribution: 'Google Earth Engine'
        }).addTo(map);
      }

      // Ajustement des limites
      const leafletBounds = L.latLngBounds(
        [bounds.south, bounds.west],
        [bounds.north, bounds.east]
      );
      map.fitBounds(leafletBounds);

      mapInstanceRef.current = map;
    } catch (error) {
      console.error('Erreur lors de la création de la carte:', error);
    }

    return () => {
      if (mapInstanceRef.current) {
        try {
          mapInstanceRef.current.remove();
          mapInstanceRef.current = null;
        } catch (error) {
          console.warn('Erreur lors du nettoyage de la carte:', error);
        }
      }
    };
  }, [mapUrl, bounds, title]);

  const scheme = colorSchemes[colorScheme];

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className={className}
    >
      <Card className="overflow-hidden">
        <div className="p-4 border-b border-slate-200">
          <h3 className="text-lg font-semibold text-slate-900 mb-2">{title}</h3>

          {/* Légende des couleurs */}
          <div className="flex items-center space-x-3">
            <div className={`h-4 w-24 bg-gradient-to-r ${scheme.gradient} rounded-full`}></div>
            <span className="text-xs text-slate-600">{scheme.description}</span>
          </div>
        </div>

        {/* Carte */}
        <div className="relative">
          <div
            ref={mapRef}
            id={containerIdRef.current}
            className="h-64 w-full"
            style={{ minHeight: '256px' }}
          />

          {!mapUrl && (
            <div className="absolute inset-0 flex items-center justify-center bg-slate-100">
              <div className="text-center">
                <div className="w-12 h-12 bg-slate-300 rounded-full mx-auto mb-2 flex items-center justify-center">
                  <svg className="w-6 h-6 text-slate-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-1.447-.894L15 4m0 13V4m0 0L9 7" />
                  </svg>
                </div>
                <p className="text-sm text-slate-500">Carte en cours de génération...</p>
              </div>
            </div>
          )}
        </div>
      </Card>
    </motion.div>
  );
};
