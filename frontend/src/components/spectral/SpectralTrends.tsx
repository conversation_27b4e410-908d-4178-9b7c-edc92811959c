import React from 'react';
import { motion } from 'framer-motion';
import { LineChart } from '../charts/ChartComponents';
import { Card } from '../ui';

interface TrendPoint {
  date: string;
  ndvi_mean: number;
  ndwi_mean: number;
  ndti_mean: number;
  image_id: number;
}

interface SpectralTrendsProps {
  trends: TrendPoint[];
  period: {
    start: string;
    end: string;
  };
  className?: string;
}

export const SpectralTrends: React.FC<SpectralTrendsProps> = ({
  trends,
  period,
  className = ''
}) => {
  // Préparation des données pour les graphiques
  const prepareChartData = () => {
    const labels = trends.map(point => 
      new Date(point.date).toLocaleDateString('fr-FR', { 
        month: 'short', 
        day: 'numeric' 
      })
    );

    return {
      labels,
      datasets: [
        {
          label: 'NDVI (Végétation)',
          data: trends.map(point => point.ndvi_mean),
          borderColor: '#10B981',
          backgroundColor: 'rgba(16, 185, 129, 0.1)',
          tension: 0.4,
        },
        {
          label: 'NDWI (Eau)',
          data: trends.map(point => point.ndwi_mean),
          borderColor: '#3B82F6',
          backgroundColor: 'rgba(59, 130, 246, 0.1)',
          tension: 0.4,
        },
        {
          label: 'NDTI (Sol)',
          data: trends.map(point => point.ndti_mean),
          borderColor: '#F59E0B',
          backgroundColor: 'rgba(245, 158, 11, 0.1)',
          tension: 0.4,
        },
      ],
    };
  };

  // Calcul des statistiques de tendance
  const calculateTrendStats = () => {
    if (trends.length < 2) return null;

    const first = trends[0];
    const last = trends[trends.length - 1];

    const ndviChange = ((last.ndvi_mean - first.ndvi_mean) / Math.abs(first.ndvi_mean)) * 100;
    const ndwiChange = ((last.ndwi_mean - first.ndwi_mean) / Math.abs(first.ndwi_mean)) * 100;
    const ndtiChange = ((last.ndti_mean - first.ndti_mean) / Math.abs(first.ndti_mean)) * 100;

    return {
      ndvi: { change: ndviChange, trend: ndviChange > 5 ? 'up' : ndviChange < -5 ? 'down' : 'stable' },
      ndwi: { change: ndwiChange, trend: ndwiChange > 5 ? 'up' : ndwiChange < -5 ? 'down' : 'stable' },
      ndti: { change: ndtiChange, trend: ndtiChange > 5 ? 'up' : ndtiChange < -5 ? 'down' : 'stable' },
    };
  };

  const trendStats = calculateTrendStats();

  const getTrendColor = (trend: string) => {
    switch (trend) {
      case 'up': return 'text-green-600';
      case 'down': return 'text-red-600';
      default: return 'text-slate-600';
    }
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up': return '↗️';
      case 'down': return '↘️';
      default: return '➡️';
    }
  };

  if (trends.length === 0) {
    return (
      <Card className={className}>
        <div className="text-center py-12">
          <div className="w-16 h-16 bg-slate-100 rounded-full mx-auto mb-4 flex items-center justify-center">
            <svg className="w-8 h-8 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-slate-900 mb-2">Aucune donnée de tendance</h3>
          <p className="text-slate-500">Pas assez d'images analysées pour afficher les tendances temporelles.</p>
        </div>
      </Card>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Graphique principal */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <LineChart
          title={`Évolution des Indices Spectraux (${new Date(period.start).toLocaleDateString('fr-FR')} - ${new Date(period.end).toLocaleDateString('fr-FR')})`}
          data={prepareChartData()}
        />
      </motion.div>

      {/* Statistiques de tendance */}
      {trendStats && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <Card>
            <h3 className="text-lg font-semibold text-slate-900 mb-4">Analyse des Tendances</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center p-4 bg-green-50 rounded-lg">
                <div className="text-2xl mb-2">🌱</div>
                <div className="font-medium text-slate-900">NDVI (Végétation)</div>
                <div className={`text-lg font-bold ${getTrendColor(trendStats.ndvi.trend)}`}>
                  {getTrendIcon(trendStats.ndvi.trend)} {Math.abs(trendStats.ndvi.change).toFixed(1)}%
                </div>
                <div className="text-sm text-slate-600 mt-1">
                  {trendStats.ndvi.trend === 'up' ? 'Amélioration' : 
                   trendStats.ndvi.trend === 'down' ? 'Dégradation' : 'Stable'}
                </div>
              </div>

              <div className="text-center p-4 bg-blue-50 rounded-lg">
                <div className="text-2xl mb-2">💧</div>
                <div className="font-medium text-slate-900">NDWI (Eau)</div>
                <div className={`text-lg font-bold ${getTrendColor(trendStats.ndwi.trend)}`}>
                  {getTrendIcon(trendStats.ndwi.trend)} {Math.abs(trendStats.ndwi.change).toFixed(1)}%
                </div>
                <div className="text-sm text-slate-600 mt-1">
                  {trendStats.ndwi.trend === 'up' ? 'Augmentation' : 
                   trendStats.ndwi.trend === 'down' ? 'Diminution' : 'Stable'}
                </div>
              </div>

              <div className="text-center p-4 bg-orange-50 rounded-lg">
                <div className="text-2xl mb-2">🏔️</div>
                <div className="font-medium text-slate-900">NDTI (Sol)</div>
                <div className={`text-lg font-bold ${getTrendColor(trendStats.ndti.trend)}`}>
                  {getTrendIcon(trendStats.ndti.trend)} {Math.abs(trendStats.ndti.change).toFixed(1)}%
                </div>
                <div className="text-sm text-slate-600 mt-1">
                  {trendStats.ndti.trend === 'up' ? 'Perturbation croissante' : 
                   trendStats.ndti.trend === 'down' ? 'Récupération' : 'Stable'}
                </div>
              </div>
            </div>

            {/* Alerte si perturbation détectée */}
            {trendStats.ndti.trend === 'up' && Math.abs(trendStats.ndti.change) > 10 && (
              <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-lg">
                <div className="flex items-center">
                  <svg className="w-5 h-5 text-red-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                  </svg>
                  <div>
                    <div className="font-medium text-red-800">Alerte : Perturbation du sol détectée</div>
                    <div className="text-sm text-red-600">
                      L'indice NDTI montre une augmentation significative, indiquant une possible activité minière.
                    </div>
                  </div>
                </div>
              </div>
            )}
          </Card>
        </motion.div>
      )}
    </div>
  );
};
