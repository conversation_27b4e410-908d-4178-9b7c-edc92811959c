import React, { useState } from 'react';
import { motion } from 'framer-motion';
import {
  DocumentArrowDownIcon,
  DocumentTextIcon,
  BanknotesIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
} from '@heroicons/react/24/outline';
import { <PERSON><PERSON>, <PERSON> } from '../ui';
import { reportService } from '../../services/report.service';
import toast from 'react-hot-toast';

interface ReportGeneratorProps {
  imageData: {
    id: number;
    name: string;
    capture_date: string;
    satellite_source: string;
    cloud_coverage: number;
    region: string;
  };
  spectralData: {
    ndvi: { mean: number; stddev: number; computed_at: string };
    ndwi: { mean: number; stddev: number; computed_at: string };
    ndti: { mean: number; stddev: number; computed_at: string };
  };
  className?: string;
}

export const ReportGenerator: React.FC<ReportGeneratorProps> = ({
  imageData,
  spectralData,
  className = ''
}) => {
  const [isGenerating, setIsGenerating] = useState(false);

  // Calcul du niveau de risque
  const calculateRiskLevel = () => {
    const { ndvi, ndwi, ndti } = spectralData;

    // Vérification de sécurité pour les valeurs nulles
    const ndviMean = ndvi?.mean ?? 0;
    const ndwiMean = ndwi?.mean ?? 0;
    const ndtiMean = ndti?.mean ?? 0;

    if (ndtiMean > 0.15 && ndviMean < 0.2) {
      return { level: 'CRITICAL', color: 'text-red-600', bgColor: 'bg-red-50', icon: ExclamationTriangleIcon };
    } else if (ndtiMean > 0.1 || ndviMean < 0.3) {
      return { level: 'HIGH', color: 'text-orange-600', bgColor: 'bg-orange-50', icon: ExclamationTriangleIcon };
    } else if (ndtiMean > 0.05 || ndviMean < 0.4) {
      return { level: 'MODERATE', color: 'text-yellow-600', bgColor: 'bg-yellow-50', icon: ExclamationTriangleIcon };
    } else {
      return { level: 'LOW', color: 'text-green-600', bgColor: 'bg-green-50', icon: CheckCircleIcon };
    }
  };

  // Estimation rapide des coûts basée sur votre configuration financière
  const estimateFinancialImpact = () => {
    const { ndvi, ndwi, ndti } = spectralData;

    // Vérification de sécurité pour les valeurs nulles
    const ndviMean = ndvi?.mean ?? 0.5;
    const ndwiMean = ndwi?.mean ?? 0;
    const ndtiMean = ndti?.mean ?? 0;

    // Configuration financière (FCFA) - basée sur votre système
    const BASE_COSTS = {
      deforestation: 9_300_000,    // 9.3M FCFA/ha
      water_pollution: 5_000_000,  // 5M FCFA/ha
      soil_degradation: 3_000_000, // 3M FCFA/ha
      biodiversity_loss: 2_000_000 // 2M FCFA/ha
    };

    // Estimation surface affectée (hectares)
    const miningDetected = ndtiMean > 0.6 && ndviMean < 0.7;
    let areaHectares = 0;
    if (miningDetected) {
      const ndviAnomalyIntensity = Math.max(0, (0.7 - ndviMean) / 0.7);
      const ndtiAnomalyIntensity = Math.max(0, ndtiMean);
      const combinedIntensity = (ndviAnomalyIntensity + ndtiAnomalyIntensity) / 2;
      areaHectares = Math.min(combinedIntensity * 50, 50);
    } else {
      areaHectares = Math.random() * 5;
    }

    // Facteur d'intensité
    let intensityFactor = 1.0;
    if (ndviMean < 0.7) intensityFactor += 0.5;
    if (ndwiMean > 0.5) intensityFactor += 0.3;
    if (ndtiMean > 0.6) intensityFactor += 0.2;

    // Calculs des coûts
    const deforestationCost = areaHectares * BASE_COSTS.deforestation * intensityFactor;
    const waterPollutionCost = areaHectares * BASE_COSTS.water_pollution * (ndwiMean > 0.1 ? 1 : 0.1);
    const soilDegradationCost = areaHectares * BASE_COSTS.soil_degradation * (ndtiMean > 0.1 ? 1 : 0.1);
    const biodiversityLossCost = areaHectares * BASE_COSTS.biodiversity_loss * intensityFactor;

    const totalCostFCFA = (deforestationCost + waterPollutionCost + soilDegradationCost + biodiversityLossCost) * 1.5; // Facteur distance

    // Retourner directement en FCFA (pas de conversion)
    return totalCostFCFA;
  };

  const handleGenerateReport = async () => {
    setIsGenerating(true);

    try {
      const reportData = {
        image: imageData,
        indices: spectralData,
        riskAssessment: {
          overallRisk: calculateRiskLevel().level as 'LOW' | 'MODERATE' | 'HIGH' | 'CRITICAL',
          riskScore: Math.min(spectralData.ndti.mean * 100, 100),
          financialImpact: {
            estimatedLoss: estimateFinancialImpact(),
            currency: 'EUR',
            timeframe: '5 ans'
          },
          recommendations: []
        }
      };

      await reportService.generateSpectralAnalysisReport(reportData);

      toast.success('Rapport PDF généré avec succès !');
    } catch (error) {
      console.error('Erreur lors de la génération du rapport:', error);
      toast.error('Erreur lors de la génération du rapport');
    } finally {
      setIsGenerating(false);
    }
  };

  const riskInfo = calculateRiskLevel();
  const estimatedCost = estimateFinancialImpact();

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className={className}
    >
      <Card>
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold text-slate-900 flex items-center">
            <DocumentTextIcon className="w-5 h-5 mr-2 text-orange-500" />
            Génération de Rapport
          </h3>
          <div className={`px-3 py-1 rounded-full text-sm font-medium ${riskInfo.bgColor} ${riskInfo.color}`}>
            <riskInfo.icon className="w-4 h-4 inline mr-1" />
            Risque {riskInfo.level}
          </div>
        </div>

        {/* Aperçu des risques financiers */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          <div className="p-4 bg-gradient-to-br from-orange-50 to-amber-50 rounded-lg border border-orange-200">
            <div className="flex items-center mb-2">
              <BanknotesIcon className="w-5 h-5 text-orange-600 mr-2" />
              <span className="font-medium text-orange-800">Impact Financier Estimé</span>
            </div>
            <div className="text-2xl font-bold text-orange-900">
              {new Intl.NumberFormat('fr-FR', {
                minimumFractionDigits: 0,
                maximumFractionDigits: 0
              }).format(estimatedCost)} FCFA
            </div>
            <div className="text-sm text-orange-700 mt-1">
              Coût de réhabilitation sur 5 ans
            </div>
          </div>

          <div className="p-4 bg-gradient-to-br from-blue-50 to-indigo-50 rounded-lg border border-blue-200">
            <div className="flex items-center mb-2">
              <ExclamationTriangleIcon className="w-5 h-5 text-blue-600 mr-2" />
              <span className="font-medium text-blue-800">Niveau d'Alerte</span>
            </div>
            <div className="text-2xl font-bold text-blue-900">
              {riskInfo.level}
            </div>
            <div className="text-sm text-blue-700 mt-1">
              Basé sur l'analyse spectrale
            </div>
          </div>
        </div>

        {/* Contenu du rapport */}
        <div className="mb-6">
          <h4 className="font-medium text-slate-900 mb-3">Le rapport inclura :</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm text-slate-600">
            <div className="flex items-center">
              <div className="w-2 h-2 bg-orange-500 rounded-full mr-2"></div>
              Analyse détaillée des indices spectraux
            </div>
            <div className="flex items-center">
              <div className="w-2 h-2 bg-orange-500 rounded-full mr-2"></div>
              Évaluation des risques financiers (FCFA + EUR)
            </div>
            <div className="flex items-center">
              <div className="w-2 h-2 bg-orange-500 rounded-full mr-2"></div>
              Coûts par type d'impact (déforestation, pollution...)
            </div>
            <div className="flex items-center">
              <div className="w-2 h-2 bg-orange-500 rounded-full mr-2"></div>
              Classification des risques (CRITIQUE/ÉLEVÉ/MOYEN/FAIBLE)
            </div>
            <div className="flex items-center">
              <div className="w-2 h-2 bg-orange-500 rounded-full mr-2"></div>
              Analyse coût-bénéfice de la prévention
            </div>
            <div className="flex items-center">
              <div className="w-2 h-2 bg-orange-500 rounded-full mr-2"></div>
              Recommandations basées sur données ministérielles
            </div>
          </div>

          {/* Paramètres financiers */}
          <div className="mt-4 p-3 bg-amber-50 rounded-lg border border-amber-200">
            <h5 className="font-medium text-amber-800 mb-2">💰 Paramètres Financiers (Ministère Ivoirien)</h5>
            <div className="grid grid-cols-2 gap-2 text-xs text-amber-700">
              <div>🌳 Déforestation: 9.3M FCFA/ha</div>
              <div>💧 Pollution hydrique: 5M FCFA/ha</div>
              <div>🏔️ Dégradation sols: 3M FCFA/ha</div>
              <div>🦋 Perte biodiversité: 2M FCFA/ha</div>
            </div>
            <div className="mt-2 text-xs text-amber-600">
              📊 Basé sur 3000 milliards FCFA/an de dommages liés à l'orpaillage
            </div>
          </div>
        </div>

        {/* Informations de l'image */}
        <div className="mb-6 p-4 bg-slate-50 rounded-lg">
          <h4 className="font-medium text-slate-900 mb-2">Image analysée :</h4>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-slate-600">Nom :</span>
              <span className="ml-2 font-medium">{imageData.name}</span>
            </div>
            <div>
              <span className="text-slate-600">Date :</span>
              <span className="ml-2 font-medium">
                {new Date(imageData.capture_date).toLocaleDateString('fr-FR')}
              </span>
            </div>
            <div>
              <span className="text-slate-600">Satellite :</span>
              <span className="ml-2 font-medium">{imageData.satellite_source}</span>
            </div>
            <div>
              <span className="text-slate-600">Nuages :</span>
              <span className="ml-2 font-medium">{imageData.cloud_coverage}%</span>
            </div>
          </div>
        </div>

        {/* Bouton de génération */}
        <div className="flex justify-center">
          <Button
            variant="primary"
            size="lg"
            onClick={handleGenerateReport}
            disabled={isGenerating}
            icon={DocumentArrowDownIcon}
            className="px-8"
          >
            {isGenerating ? 'Génération en cours...' : 'Générer le Rapport PDF'}
          </Button>
        </div>

        {/* Note */}
        <div className="mt-4 text-xs text-slate-500 text-center">
          Le rapport sera téléchargé automatiquement une fois généré
        </div>
      </Card>
    </motion.div>
  );
};
