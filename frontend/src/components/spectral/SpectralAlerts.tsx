import React from 'react';
import { motion } from 'framer-motion';
import {
  ExclamationTriangleIcon,
  ShieldCheckIcon,
  EyeIcon,
  MapPinIcon,
  ClockIcon,
  ChartBarIcon,
} from '@heroicons/react/24/outline';
import { <PERSON>, Button } from '../ui';

interface Alert {
  id: string;
  type: 'critical' | 'warning' | 'info';
  title: string;
  description: string;
  location: {
    lat: number;
    lon: number;
    name: string;
  };
  detectedAt: string;
  confidence: number;
  indices: {
    ndvi: number;
    ndwi: number;
    ndti: number;
  };
  anomalyScore: number;
}

interface SpectralAlertsProps {
  alerts: Alert[];
  onViewAlert: (alertId: string) => void;
  onViewLocation: (lat: number, lon: number) => void;
  className?: string;
}

export const SpectralAlerts: React.FC<SpectralAlertsProps> = ({
  alerts,
  onViewAlert,
  onViewLocation,
  className = ''
}) => {
  const getAlertIcon = (type: string) => {
    switch (type) {
      case 'critical':
        return <ExclamationTriangleIcon className="w-5 h-5 text-red-500" />;
      case 'warning':
        return <ExclamationTriangleIcon className="w-5 h-5 text-yellow-500" />;
      default:
        return <ShieldCheckIcon className="w-5 h-5 text-blue-500" />;
    }
  };

  const getAlertColor = (type: string) => {
    switch (type) {
      case 'critical':
        return 'border-red-200 bg-red-50';
      case 'warning':
        return 'border-yellow-200 bg-yellow-50';
      default:
        return 'border-blue-200 bg-blue-50';
    }
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) return 'text-red-600 bg-red-100';
    if (confidence >= 0.6) return 'text-yellow-600 bg-yellow-100';
    return 'text-green-600 bg-green-100';
  };

  if (alerts.length === 0) {
    return (
      <Card className={className}>
        <div className="text-center py-12">
          <ShieldCheckIcon className="w-16 h-16 text-green-500 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-slate-900 mb-2">Aucune alerte détectée</h3>
          <p className="text-slate-500">Tous les indices spectraux sont dans les normes</p>
        </div>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-slate-900 flex items-center">
          <ExclamationTriangleIcon className="w-5 h-5 mr-2 text-red-500" />
          Alertes Spectrales ({alerts.length})
        </h3>
        <div className="flex items-center space-x-2">
          <div className="flex items-center text-sm text-slate-600">
            <div className="w-3 h-3 bg-red-500 rounded-full mr-1"></div>
            <span>Critique</span>
          </div>
          <div className="flex items-center text-sm text-slate-600">
            <div className="w-3 h-3 bg-yellow-500 rounded-full mr-1"></div>
            <span>Attention</span>
          </div>
        </div>
      </div>

      <div className="space-y-4">
        {alerts.map((alert, index) => (
          <motion.div
            key={alert.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            className={`p-4 rounded-lg border-2 ${getAlertColor(alert.type)}`}
          >
            <div className="flex items-start justify-between">
              <div className="flex items-start space-x-3 flex-1">
                <div className="mt-0.5">
                  {getAlertIcon(alert.type)}
                </div>
                <div className="flex-1">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-semibold text-slate-900">{alert.title}</h4>
                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${getConfidenceColor(alert.confidence)}`}>
                      {Math.round(alert.confidence * 100)}% confiance
                    </span>
                  </div>
                  
                  <p className="text-sm text-slate-700 mb-3">{alert.description}</p>
                  
                  {/* Informations de localisation */}
                  <div className="flex items-center space-x-4 text-sm text-slate-600 mb-3">
                    <span className="flex items-center">
                      <MapPinIcon className="w-4 h-4 mr-1" />
                      {alert.location.name}
                    </span>
                    <span className="flex items-center">
                      <ClockIcon className="w-4 h-4 mr-1" />
                      {new Date(alert.detectedAt).toLocaleDateString('fr-FR')}
                    </span>
                  </div>

                  {/* Indices spectraux */}
                  <div className="grid grid-cols-3 gap-3 mb-4">
                    <div className="text-center p-2 bg-white rounded border">
                      <div className="text-xs text-slate-600">NDVI</div>
                      <div className="font-semibold text-green-600">
                        {alert.indices.ndvi.toFixed(3)}
                      </div>
                    </div>
                    <div className="text-center p-2 bg-white rounded border">
                      <div className="text-xs text-slate-600">NDWI</div>
                      <div className="font-semibold text-blue-600">
                        {alert.indices.ndwi.toFixed(3)}
                      </div>
                    </div>
                    <div className="text-center p-2 bg-white rounded border">
                      <div className="text-xs text-slate-600">NDTI</div>
                      <div className="font-semibold text-orange-600">
                        {alert.indices.ndti.toFixed(3)}
                      </div>
                    </div>
                  </div>

                  {/* Score d'anomalie */}
                  <div className="mb-4">
                    <div className="flex items-center justify-between text-sm mb-1">
                      <span className="text-slate-600">Score d'anomalie</span>
                      <span className="font-medium">{Math.round(alert.anomalyScore * 100)}%</span>
                    </div>
                    <div className="w-full bg-slate-200 rounded-full h-2">
                      <div
                        className={`h-2 rounded-full transition-all duration-500 ${
                          alert.anomalyScore > 0.7 ? 'bg-red-500' :
                          alert.anomalyScore > 0.4 ? 'bg-yellow-500' : 'bg-green-500'
                        }`}
                        style={{ width: `${alert.anomalyScore * 100}%` }}
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Actions */}
            <div className="flex items-center justify-end space-x-2 pt-3 border-t border-white/50">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onViewLocation(alert.location.lat, alert.location.lon)}
                icon={MapPinIcon}
              >
                Localiser
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onViewAlert(alert.id)}
                icon={EyeIcon}
              >
                Détails
              </Button>
              <Button
                variant="secondary"
                size="sm"
                icon={ChartBarIcon}
              >
                Analyser
              </Button>
            </div>
          </motion.div>
        ))}
      </div>

      {/* Résumé des alertes */}
      <div className="mt-6 pt-6 border-t border-slate-200">
        <div className="grid grid-cols-3 gap-4 text-center">
          <div>
            <div className="text-2xl font-bold text-red-600">
              {alerts.filter(a => a.type === 'critical').length}
            </div>
            <div className="text-sm text-slate-600">Critiques</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-yellow-600">
              {alerts.filter(a => a.type === 'warning').length}
            </div>
            <div className="text-sm text-slate-600">Attention</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-slate-600">
              {alerts.filter(a => a.confidence > 0.8).length}
            </div>
            <div className="text-sm text-slate-600">Haute confiance</div>
          </div>
        </div>
      </div>
    </Card>
  );
};
