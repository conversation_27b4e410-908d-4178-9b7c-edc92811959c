import React, { useState } from 'react';
import { motion } from 'framer-motion';
import {
  CalendarIcon,
  CloudIcon,
  AdjustmentsHorizontalIcon,
  PlayIcon,
  StopIcon,
  ArrowPathIcon,
} from '@heroicons/react/24/outline';
import { Card, Button } from '../ui';
import toast from 'react-hot-toast';

interface SpectralControlsProps {
  onDateRangeChange: (startDate: string, endDate: string) => void;
  onCloudCoverageChange: (maxCoverage: number) => void;
  onProcessImage: (imageId: number) => void;
  onRefreshImages: () => void;
  isProcessing?: boolean;
  className?: string;
}

export const SpectralControls: React.FC<SpectralControlsProps> = ({
  onDateRangeChange,
  onCloudCoverageChange,
  onProcessImage,
  onRefreshImages,
  isProcessing = false,
  className = ''
}) => {
  const [startDate, setStartDate] = useState(() => {
    const date = new Date();
    date.setMonth(date.getMonth() - 3);
    return date.toISOString().split('T')[0];
  });
  
  const [endDate, setEndDate] = useState(() => {
    return new Date().toISOString().split('T')[0];
  });
  
  const [maxCloudCoverage, setMaxCloudCoverage] = useState(20);
  const [autoRefresh, setAutoRefresh] = useState(false);

  const handleDateChange = () => {
    if (new Date(startDate) >= new Date(endDate)) {
      toast.error('La date de début doit être antérieure à la date de fin');
      return;
    }
    onDateRangeChange(startDate, endDate);
    toast.success('Période mise à jour');
  };

  const handleCloudCoverageChange = (value: number) => {
    setMaxCloudCoverage(value);
    onCloudCoverageChange(value);
    toast.success(`Couverture nuageuse max: ${value}%`);
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className={className}
    >
      <Card>
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold text-slate-900 flex items-center">
            <AdjustmentsHorizontalIcon className="w-5 h-5 mr-2 text-orange-500" />
            Contrôles d'Analyse
          </h3>
          <div className="flex space-x-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={onRefreshImages}
              icon={ArrowPathIcon}
              disabled={isProcessing}
            >
              Actualiser
            </Button>
            <Button
              variant={autoRefresh ? 'danger' : 'primary'}
              size="sm"
              onClick={() => setAutoRefresh(!autoRefresh)}
              icon={autoRefresh ? StopIcon : PlayIcon}
            >
              {autoRefresh ? 'Arrêter' : 'Auto-refresh'}
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Sélection de période */}
          <div className="space-y-4">
            <h4 className="font-medium text-slate-900 flex items-center">
              <CalendarIcon className="w-4 h-4 mr-2" />
              Période d'analyse
            </h4>
            <div className="space-y-3">
              <div>
                <label className="block text-sm text-slate-600 mb-1">Date de début</label>
                <input
                  type="date"
                  value={startDate}
                  onChange={(e) => setStartDate(e.target.value)}
                  className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                />
              </div>
              <div>
                <label className="block text-sm text-slate-600 mb-1">Date de fin</label>
                <input
                  type="date"
                  value={endDate}
                  onChange={(e) => setEndDate(e.target.value)}
                  className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                />
              </div>
              <Button
                variant="secondary"
                size="sm"
                onClick={handleDateChange}
                className="w-full"
              >
                Appliquer la période
              </Button>
            </div>
          </div>

          {/* Contrôle couverture nuageuse */}
          <div className="space-y-4">
            <h4 className="font-medium text-slate-900 flex items-center">
              <CloudIcon className="w-4 h-4 mr-2" />
              Couverture nuageuse
            </h4>
            <div className="space-y-3">
              <div>
                <label className="block text-sm text-slate-600 mb-2">
                  Maximum autorisé: {maxCloudCoverage}%
                </label>
                <input
                  type="range"
                  min="0"
                  max="50"
                  step="5"
                  value={maxCloudCoverage}
                  onChange={(e) => handleCloudCoverageChange(Number(e.target.value))}
                  className="w-full h-2 bg-slate-200 rounded-lg appearance-none cursor-pointer slider"
                />
                <div className="flex justify-between text-xs text-slate-500 mt-1">
                  <span>0%</span>
                  <span>25%</span>
                  <span>50%</span>
                </div>
              </div>
              <div className="text-sm text-slate-600">
                <p>• 0-10% : Excellente qualité</p>
                <p>• 10-20% : Bonne qualité</p>
                <p>• 20-30% : Qualité acceptable</p>
              </div>
            </div>
          </div>

          {/* Paramètres avancés */}
          <div className="space-y-4">
            <h4 className="font-medium text-slate-900">Paramètres avancés</h4>
            <div className="space-y-3">
              <div className="flex items-center justify-between p-3 bg-slate-50 rounded-lg">
                <div>
                  <div className="font-medium text-sm">Résolution</div>
                  <div className="text-xs text-slate-600">Sentinel-2: 10m</div>
                </div>
                <div className="text-sm font-medium text-orange-600">10m</div>
              </div>
              
              <div className="flex items-center justify-between p-3 bg-slate-50 rounded-lg">
                <div>
                  <div className="font-medium text-sm">Collection</div>
                  <div className="text-xs text-slate-600">COPERNICUS/S2_SR</div>
                </div>
                <div className="text-sm font-medium text-orange-600">Active</div>
              </div>
              
              <div className="flex items-center justify-between p-3 bg-slate-50 rounded-lg">
                <div>
                  <div className="font-medium text-sm">Zone d'étude</div>
                  <div className="text-xs text-slate-600">Bondoukou, Côte d'Ivoire</div>
                </div>
                <div className="text-sm font-medium text-orange-600">12,000 km²</div>
              </div>
            </div>
          </div>
        </div>

        {/* Indicateurs de statut */}
        <div className="mt-6 pt-6 border-t border-slate-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="flex items-center">
                <div className="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                <span className="text-sm text-slate-600">Google Earth Engine</span>
              </div>
              <div className="flex items-center">
                <div className={`w-3 h-3 rounded-full mr-2 ${isProcessing ? 'bg-yellow-500' : 'bg-green-500'}`}></div>
                <span className="text-sm text-slate-600">
                  {isProcessing ? 'Traitement en cours' : 'Prêt'}
                </span>
              </div>
            </div>
            
            {autoRefresh && (
              <div className="flex items-center text-sm text-orange-600">
                <div className="w-2 h-2 bg-orange-500 rounded-full mr-2 animate-pulse"></div>
                Auto-refresh actif
              </div>
            )}
          </div>
        </div>
      </Card>
    </motion.div>
  );
};
