import React from 'react';
import { motion } from 'framer-motion';
import {
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon,
  MinusIcon,
} from '@heroicons/react/24/outline';
import { Card } from '../ui';

interface IndexData {
  mean: number;
  stddev: number;
  computed_at: string;
}

interface SpectralIndicesProps {
  ndvi: IndexData;
  ndwi: IndexData;
  ndti: IndexData;
  className?: string;
}

interface IndexCardProps {
  title: string;
  value: number;
  stddev: number;
  icon: React.ElementType;
  color: string;
  interpretation: {
    status: string;
    description: string;
  };
  trend?: 'up' | 'down' | 'stable';
}

const IndexCard: React.FC<IndexCardProps> = ({
  title,
  value,
  stddev,
  icon: Icon,
  color,
  interpretation,
  trend
}) => {
  const getTrendIcon = () => {
    switch (trend) {
      case 'up':
        return <ArrowTrendingUpIcon className="w-4 h-4 text-green-500" />;
      case 'down':
        return <ArrowTrendingDownIcon className="w-4 h-4 text-red-500" />;
      default:
        return <MinusIcon className="w-4 h-4 text-slate-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'excellent':
      case 'good':
      case 'natural':
        return 'text-green-600 bg-green-50';
      case 'moderate':
      case 'low':
      case 'slight':
        return 'text-yellow-600 bg-yellow-50';
      case 'poor':
      case 'dry':
      case 'modified':
        return 'text-orange-600 bg-orange-50';
      case 'critical':
      case 'disturbed':
        return 'text-red-600 bg-red-50';
      default:
        return 'text-slate-600 bg-slate-50';
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.3 }}
    >
      <Card className="h-full hover:shadow-lg transition-shadow">
        <div className="flex items-start justify-between mb-4">
          <div className="flex items-center space-x-3">
            <div className={`p-2 rounded-lg ${color}`}>
              <Icon className="w-5 h-5 text-white" />
            </div>
            <div>
              <h3 className="font-semibold text-slate-900">{title}</h3>
              <div className="flex items-center space-x-1 mt-1">
                {getTrendIcon()}
                <span className="text-xs text-slate-500">Tendance</span>
              </div>
            </div>
          </div>
        </div>

        <div className="space-y-3">
          {/* Valeur principale */}
          <div>
            <div className="text-2xl font-bold text-slate-900">
              {value.toFixed(3)}
            </div>
            <div className="text-sm text-slate-500">
              ±{stddev.toFixed(3)} (écart-type)
            </div>
          </div>

          {/* Barre de progression */}
          <div className="w-full bg-slate-200 rounded-full h-2">
            <div
              className={`h-2 rounded-full transition-all duration-500 ${
                interpretation.status === 'excellent' || interpretation.status === 'good'
                  ? 'bg-green-500'
                  : interpretation.status === 'moderate'
                  ? 'bg-yellow-500'
                  : interpretation.status === 'poor'
                  ? 'bg-orange-500'
                  : 'bg-red-500'
              }`}
              style={{
                width: `${Math.min(Math.abs(value) * 100, 100)}%`
              }}
            />
          </div>

          {/* Statut et description */}
          <div>
            <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(interpretation.status)}`}>
              {interpretation.status}
            </span>
            <p className="text-sm text-slate-600 mt-2">
              {interpretation.description}
            </p>
          </div>
        </div>
      </Card>
    </motion.div>
  );
};

export const SpectralIndices: React.FC<SpectralIndicesProps> = ({
  ndvi,
  ndwi,
  ndti,
  className = ''
}) => {
  // Fonction d'interprétation des indices
  const interpretIndex = (value: number, indexType: 'ndvi' | 'ndwi' | 'ndti') => {
    switch (indexType) {
      case 'ndvi':
        if (value > 0.6) return { status: 'excellent', description: 'Végétation très dense et saine' };
        if (value > 0.4) return { status: 'good', description: 'Végétation dense' };
        if (value > 0.2) return { status: 'moderate', description: 'Végétation modérée' };
        if (value > 0) return { status: 'poor', description: 'Végétation clairsemée' };
        return { status: 'critical', description: 'Absence de végétation ou sol nu' };

      case 'ndwi':
        if (value > 0.3) return { status: 'high', description: 'Forte présence d\'eau ou humidité élevée' };
        if (value > 0.1) return { status: 'moderate', description: 'Présence d\'eau modérée' };
        if (value > -0.1) return { status: 'low', description: 'Faible humidité' };
        return { status: 'dry', description: 'Zone sèche, absence d\'eau' };

      case 'ndti':
        if (value > 0.2) return { status: 'disturbed', description: 'Sol fortement perturbé - Activité minière probable' };
        if (value > 0.1) return { status: 'modified', description: 'Sol modifié - Surveillance recommandée' };
        if (value > 0) return { status: 'slight', description: 'Légère modification du sol' };
        return { status: 'natural', description: 'Sol dans son état naturel' };

      default:
        return { status: 'unknown', description: 'Valeur inconnue' };
    }
  };

  // Créons des icônes simples avec SVG
  const VegetationIcon = () => (
    <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 24 24">
      <path d="M12 2L13.09 8.26L22 9L13.09 9.74L12 16L10.91 9.74L2 9L10.91 8.26L12 2Z"/>
    </svg>
  );

  const WaterIcon = () => (
    <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 24 24">
      <path d="M12 2C9.38 2 7.25 4.13 7.25 6.75C7.25 9.32 9.26 11.4 11.88 11.49C11.92 11.49 11.96 11.5 12 11.5C12.04 11.5 12.08 11.49 12.13 11.49C14.74 11.4 16.75 9.32 16.75 6.75C16.75 4.13 14.63 2 12 2ZM12 22C8.69 22 6 19.31 6 16S8.69 10 12 10S18 12.69 18 16S15.31 22 12 22Z"/>
    </svg>
  );

  const SoilIcon = () => (
    <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 24 24">
      <path d="M2 12H4V17H2V12ZM5 7H7V17H5V7ZM8 10H10V17H8V10ZM11 8H13V17H11V8ZM14 11H16V17H14V11ZM17 9H19V17H17V9ZM20 6H22V17H20V6Z"/>
    </svg>
  );

  return (
    <div className={`grid grid-cols-1 md:grid-cols-3 gap-6 ${className}`}>
      <IndexCard
        title="NDVI - Végétation"
        value={ndvi.mean}
        stddev={ndvi.stddev}
        icon={VegetationIcon}
        color="bg-green-500"
        interpretation={interpretIndex(ndvi.mean, 'ndvi')}
        trend="stable"
      />

      <IndexCard
        title="NDWI - Eau"
        value={ndwi.mean}
        stddev={ndwi.stddev}
        icon={WaterIcon}
        color="bg-blue-500"
        interpretation={interpretIndex(ndwi.mean, 'ndwi')}
        trend="down"
      />

      <IndexCard
        title="NDTI - Sol"
        value={ndti.mean}
        stddev={ndti.stddev}
        icon={SoilIcon}
        color="bg-orange-500"
        interpretation={interpretIndex(ndti.mean, 'ndti')}
        trend="up"
      />
    </div>
  );
};
