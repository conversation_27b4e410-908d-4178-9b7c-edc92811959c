import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  MapPinIcon,
  MagnifyingGlassIcon,
  AdjustmentsHorizontalIcon,
} from '@heroicons/react/24/outline';
import { Card, Button, Badge } from '../ui';
import {
  Detection,
  DETECTION_TYPE_LABELS,
  DETECTION_TYPE_COLORS,
  getConfidenceLevel,
  getDetectionTypeIcon,
} from '../../types/detection.types';

interface DetectionMapProps {
  detections: Detection[];
  selectedDetection?: Detection | null;
  onDetectionSelect: (detection: Detection) => void;
  onDetectionHover?: (detection: Detection | null) => void;
  isLoading?: boolean;
}

// Composant de marqueur de détection
const DetectionMarker: React.FC<{
  detection: Detection;
  isSelected: boolean;
  onClick: () => void;
  onHover: () => void;
  onLeave: () => void;
}> = ({ detection, isSelected, onClick, onHover, onLeave }) => {
  const confidenceLevel = getConfidenceLevel(detection.confidence_score);
  
  return (
    <motion.div
      className={`absolute transform -translate-x-1/2 -translate-y-1/2 cursor-pointer ${
        isSelected ? 'z-20' : 'z-10'
      }`}
      style={{
        left: `${((detection.longitude + 180) / 360) * 100}%`,
        top: `${((90 - detection.latitude) / 180) * 100}%`,
      }}
      whileHover={{ scale: 1.2 }}
      whileTap={{ scale: 0.9 }}
      onClick={onClick}
      onMouseEnter={onHover}
      onMouseLeave={onLeave}
    >
      <div
        className={`w-6 h-6 rounded-full border-2 border-white shadow-lg flex items-center justify-center text-xs ${
          isSelected
            ? 'ring-2 ring-blue-500 ring-offset-2'
            : ''
        }`}
        style={{
          backgroundColor: confidenceLevel.color === 'green' ? '#10B981' :
                          confidenceLevel.color === 'yellow' ? '#F59E0B' : '#EF4444',
        }}
      >
        <span className="text-white font-bold">
          {getDetectionTypeIcon(detection.detection_type)}
        </span>
      </div>
      
      {isSelected && (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          className="absolute top-8 left-1/2 transform -translate-x-1/2 bg-white rounded-lg shadow-lg p-3 min-w-48 border"
        >
          <div className="text-sm">
            <div className="font-semibold text-slate-900 mb-1">
              {DETECTION_TYPE_LABELS[detection.detection_type]}
            </div>
            <div className="text-slate-600 mb-2">
              {detection.region_name}
            </div>
            <div className="flex items-center justify-between">
              <Badge variant={confidenceLevel.color as any} className="text-xs">
                {(detection.confidence_score * 100).toFixed(0)}%
              </Badge>
              <span className="text-xs text-slate-500">
                {detection.area_hectares.toFixed(1)} ha
              </span>
            </div>
          </div>
        </motion.div>
      )}
    </motion.div>
  );
};

export const DetectionMap: React.FC<DetectionMapProps> = ({
  detections,
  selectedDetection,
  onDetectionSelect,
  onDetectionHover,
  isLoading = false,
}) => {
  const [hoveredDetection, setHoveredDetection] = useState<Detection | null>(null);
  const [mapCenter, setMapCenter] = useState({ lat: 8.0, lng: -5.0 }); // Côte d'Ivoire
  const [mapZoom, setMapZoom] = useState(7);

  // Statistiques des détections visibles
  const stats = {
    total: detections.length,
    highConfidence: detections.filter(d => d.confidence_score >= 0.8).length,
    pending: detections.filter(d => d.validation_status === 'DETECTED').length,
  };

  const handleMarkerClick = (detection: Detection) => {
    onDetectionSelect(detection);
  };

  const handleMarkerHover = (detection: Detection) => {
    setHoveredDetection(detection);
    onDetectionHover?.(detection);
  };

  const handleMarkerLeave = () => {
    setHoveredDetection(null);
    onDetectionHover?.(null);
  };

  return (
    <Card className="h-full flex flex-col">
      {/* En-tête de la carte */}
      <div className="p-4 border-b border-slate-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <MapPinIcon className="w-5 h-5 text-slate-600" />
            <h3 className="font-semibold text-slate-900">Carte des détections</h3>
          </div>
          
          <div className="flex items-center space-x-2">
            <Button variant="ghost" size="sm">
              <AdjustmentsHorizontalIcon className="w-4 h-4" />
            </Button>
            <Button variant="ghost" size="sm">
              <MagnifyingGlassIcon className="w-4 h-4" />
            </Button>
          </div>
        </div>

        {/* Statistiques rapides */}
        <div className="flex items-center space-x-4 mt-3 text-sm">
          <span className="text-slate-600">
            <span className="font-medium text-slate-900">{stats.total}</span> détections
          </span>
          <span className="text-slate-600">
            <span className="font-medium text-green-600">{stats.highConfidence}</span> haute confiance
          </span>
          <span className="text-slate-600">
            <span className="font-medium text-orange-600">{stats.pending}</span> en attente
          </span>
        </div>
      </div>

      {/* Carte */}
      <div className="flex-1 relative bg-slate-100 overflow-hidden">
        {isLoading ? (
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="text-slate-500">Chargement de la carte...</div>
          </div>
        ) : (
          <>
            {/* Fond de carte simplifié */}
            <div className="absolute inset-0 bg-gradient-to-br from-blue-100 to-green-100">
              {/* Grille de fond */}
              <div className="absolute inset-0 opacity-20">
                {Array.from({ length: 10 }).map((_, i) => (
                  <div
                    key={`h-${i}`}
                    className="absolute w-full border-t border-slate-300"
                    style={{ top: `${i * 10}%` }}
                  />
                ))}
                {Array.from({ length: 10 }).map((_, i) => (
                  <div
                    key={`v-${i}`}
                    className="absolute h-full border-l border-slate-300"
                    style={{ left: `${i * 10}%` }}
                  />
                ))}
              </div>

              {/* Contours simplifiés de la Côte d'Ivoire */}
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="w-64 h-48 border-2 border-slate-400 rounded-lg bg-green-50 opacity-60 flex items-center justify-center">
                  <span className="text-slate-600 font-medium">Côte d'Ivoire</span>
                </div>
              </div>
            </div>

            {/* Marqueurs de détections */}
            {detections.map((detection) => (
              <DetectionMarker
                key={detection.id}
                detection={detection}
                isSelected={selectedDetection?.id === detection.id}
                onClick={() => handleMarkerClick(detection)}
                onHover={() => handleMarkerHover(detection)}
                onLeave={handleMarkerLeave}
              />
            ))}

            {/* Légende */}
            <div className="absolute bottom-4 left-4 bg-white rounded-lg shadow-lg p-3 border">
              <h4 className="text-sm font-semibold text-slate-900 mb-2">Légende</h4>
              <div className="space-y-1">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 rounded-full bg-green-500"></div>
                  <span className="text-xs text-slate-600">Haute confiance (≥80%)</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
                  <span className="text-xs text-slate-600">Moyenne confiance (50-80%)</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 rounded-full bg-red-500"></div>
                  <span className="text-xs text-slate-600">Faible confiance (&lt;50%)</span>
                </div>
              </div>
            </div>

            {/* Contrôles de zoom */}
            <div className="absolute top-4 right-4 bg-white rounded-lg shadow-lg border">
              <button
                onClick={() => setMapZoom(Math.min(mapZoom + 1, 12))}
                className="block w-8 h-8 flex items-center justify-center text-slate-600 hover:text-slate-900 hover:bg-slate-50 rounded-t-lg border-b"
              >
                +
              </button>
              <button
                onClick={() => setMapZoom(Math.max(mapZoom - 1, 3))}
                className="block w-8 h-8 flex items-center justify-center text-slate-600 hover:text-slate-900 hover:bg-slate-50 rounded-b-lg"
              >
                −
              </button>
            </div>
          </>
        )}
      </div>

      {/* Pied de carte */}
      <div className="p-3 border-t border-slate-200 bg-slate-50">
        <div className="flex items-center justify-between text-xs text-slate-500">
          <span>
            {hoveredDetection
              ? `${DETECTION_TYPE_LABELS[hoveredDetection.detection_type]} - ${hoveredDetection.region_name}`
              : 'Survolez une détection pour plus d\'informations'
            }
          </span>
          <span>Zoom: {mapZoom}</span>
        </div>
      </div>
    </Card>
  );
};
