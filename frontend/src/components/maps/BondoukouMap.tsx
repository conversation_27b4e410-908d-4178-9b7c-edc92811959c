import React, { useEffect, useRef } from 'react';
import L from 'leaflet';
import 'leaflet/dist/leaflet.css';
import { Detection } from '../../services/detection.service';

// Fix pour les icônes Leaflet
delete (L.Icon.Default.prototype as any)._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
});

interface BondoukouMapProps {
  detections: Detection[];
  center: [number, number];
  zoom: number;
  className?: string;
}

export const BondoukouMap: React.FC<BondoukouMapProps> = ({
  detections,
  center,
  zoom,
  className = '',
}) => {
  const mapRef = useRef<HTMLDivElement>(null);
  const mapInstanceRef = useRef<L.Map | null>(null);
  const markersRef = useRef<L.LayerGroup | null>(null);

  useEffect(() => {
    if (!mapRef.current) return;

    // Initialiser la carte
    if (!mapInstanceRef.current) {
      mapInstanceRef.current = L.map(mapRef.current).setView(center, zoom);

      // Ajouter les tuiles OpenStreetMap
      L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '© OpenStreetMap contributors',
        maxZoom: 18,
      }).addTo(mapInstanceRef.current);

      // Initialiser le groupe de marqueurs
      markersRef.current = L.layerGroup().addTo(mapInstanceRef.current);
    }

    return () => {
      if (mapInstanceRef.current) {
        mapInstanceRef.current.remove();
        mapInstanceRef.current = null;
      }
    };
  }, [center, zoom]);

  useEffect(() => {
    if (!mapInstanceRef.current || !markersRef.current) return;

    // Nettoyer les marqueurs existants
    markersRef.current.clearLayers();

    // Ajouter les marqueurs pour chaque détection
    detections.forEach((detection) => {
      if (!detection.latitude || !detection.longitude) return;

      // Couleur selon le type de détection
      const getMarkerColor = (type: string) => {
        switch (type) {
          case 'MINING_SITE':
            return '#EF4444'; // Rouge
          case 'WATER_POLLUTION':
            return '#3B82F6'; // Bleu
          case 'DEFORESTATION':
            return '#10B981'; // Vert
          case 'SOIL_DISTURBANCE':
            return '#F59E0B'; // Orange
          default:
            return '#6B7280'; // Gris
        }
      };

      // Couleur selon le statut de validation
      const getStatusColor = (status: string) => {
        switch (status) {
          case 'VALIDATED':
            return '#10B981'; // Vert
          case 'CONFIRMED':
            return '#059669'; // Vert foncé
          case 'FALSE_POSITIVE':
            return '#EF4444'; // Rouge
          default:
            return '#F59E0B'; // Orange
        }
      };

      // Créer l'icône personnalisée
      const customIcon = L.divIcon({
        className: 'custom-marker',
        html: `
          <div style="
            width: 20px;
            height: 20px;
            background-color: ${getMarkerColor(detection.detection_type)};
            border: 3px solid ${getStatusColor(detection.validation_status)};
            border-radius: 50%;
            box-shadow: 0 2px 4px rgba(0,0,0,0.3);
          "></div>
        `,
        iconSize: [20, 20],
        iconAnchor: [10, 10],
      });

      // Créer le popup avec informations détaillées
      const popupContent = `
        <div class="p-3 min-w-64">
          <h3 class="font-bold text-lg mb-2 text-gray-900">
            Détection #${detection.id}
          </h3>
          
          <div class="space-y-2 text-sm">
            <div class="flex justify-between">
              <span class="font-medium text-gray-600">Type:</span>
              <span class="px-2 py-1 rounded text-xs font-medium" style="
                background-color: ${getMarkerColor(detection.detection_type)}20;
                color: ${getMarkerColor(detection.detection_type)};
              ">
                ${detection.detection_type.replace('_', ' ')}
              </span>
            </div>
            
            <div class="flex justify-between">
              <span class="font-medium text-gray-600">Statut:</span>
              <span class="px-2 py-1 rounded text-xs font-medium" style="
                background-color: ${getStatusColor(detection.validation_status)}20;
                color: ${getStatusColor(detection.validation_status)};
              ">
                ${detection.validation_status}
              </span>
            </div>
            
            <div class="flex justify-between">
              <span class="font-medium text-gray-600">Confiance:</span>
              <span class="font-bold text-blue-600">
                ${Math.round(detection.confidence_score * 100)}%
              </span>
            </div>
            
            <div class="flex justify-between">
              <span class="font-medium text-gray-600">Surface:</span>
              <span class="font-medium text-gray-900">
                ${detection.area_hectares.toFixed(2)} ha
              </span>
            </div>
            
            <div class="flex justify-between">
              <span class="font-medium text-gray-600">Date:</span>
              <span class="text-gray-700">
                ${new Date(detection.detection_date).toLocaleDateString('fr-FR')}
              </span>
            </div>
            
            <div class="pt-2 border-t border-gray-200">
              <div class="text-xs text-gray-500">
                <div>Lat: ${detection.latitude.toFixed(6)}</div>
                <div>Lng: ${detection.longitude.toFixed(6)}</div>
              </div>
            </div>
          </div>
        </div>
      `;

      // Ajouter le marqueur à la carte
      const marker = L.marker([detection.latitude, detection.longitude], {
        icon: customIcon,
      }).bindPopup(popupContent, {
        maxWidth: 300,
        className: 'custom-popup',
      });

      markersRef.current!.addLayer(marker);
    });

    // Ajuster la vue pour inclure tous les marqueurs
    if (detections.length > 0) {
      const group = new L.featureGroup(markersRef.current.getLayers());
      if (group.getBounds().isValid()) {
        mapInstanceRef.current.fitBounds(group.getBounds(), {
          padding: [20, 20],
          maxZoom: 15,
        });
      }
    }
  }, [detections]);

  return (
    <div className={`relative ${className}`}>
      <div ref={mapRef} className="w-full h-full rounded-lg" />
      
      {/* Légende */}
      <div className="absolute top-4 right-4 bg-white rounded-lg shadow-lg p-3 z-[1000]">
        <h4 className="font-semibold text-sm mb-2 text-gray-900">Légende</h4>
        <div className="space-y-1 text-xs">
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-red-500 rounded-full"></div>
            <span>Site minier</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
            <span>Pollution eau</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-green-500 rounded-full"></div>
            <span>Déforestation</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-orange-500 rounded-full"></div>
            <span>Perturbation sol</span>
          </div>
        </div>
        
        <div className="mt-3 pt-2 border-t border-gray-200">
          <div className="text-xs text-gray-600">
            <div>Bordure verte: Validé</div>
            <div>Bordure orange: En attente</div>
            <div>Bordure rouge: Faux positif</div>
          </div>
        </div>
      </div>

      {/* Compteur de détections */}
      <div className="absolute bottom-4 left-4 bg-white rounded-lg shadow-lg p-3 z-[1000]">
        <div className="text-sm font-medium text-gray-900">
          {detections.length} détection{detections.length > 1 ? 's' : ''} affichée{detections.length > 1 ? 's' : ''}
        </div>
        <div className="text-xs text-gray-600">
          Région de Bondoukou
        </div>
      </div>
    </div>
  );
};
