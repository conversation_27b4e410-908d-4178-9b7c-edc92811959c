import React, { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { motion, AnimatePresence } from 'framer-motion';
import {
  BellIcon,
  BellAlertIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  ClockIcon,
  EyeIcon,
  XMarkIcon,
} from '@heroicons/react/24/outline';
import alertService from '../../services/alert.service';
import type { Alert } from '../../services/alert.service';
import { Card, Button, Loading } from '../ui';
import { formatDistanceToNow } from 'date-fns';
import { fr } from 'date-fns/locale';

interface NotificationCenterProps {
  onAlertClick: (alert: Alert) => void;
  className?: string;
}

export const NotificationCenter: React.FC<NotificationCenterProps> = ({
  onAlertClick,
  className = '',
}) => {
  const [lastCheck, setLastCheck] = useState<string>(new Date().toISOString());
  const [dismissedNotifications, setDismissedNotifications] = useState<Set<number>>(new Set());

  // Récupération des alertes récentes
  const { 
    data: recentAlerts, 
    isLoading,
    refetch 
  } = useQuery({
    queryKey: ['recent-alerts', lastCheck],
    queryFn: () => alertService.getRecentAlerts(lastCheck),
    refetchInterval: 5000, // Refresh toutes les 5 secondes
  });

  // Récupération des alertes non lues
  const { data: unreadAlerts } = useQuery({
    queryKey: ['unread-alerts'],
    queryFn: () => alertService.getUnreadAlerts(),
    refetchInterval: 10000, // Refresh toutes les 10 secondes
  });

  // Marquer le moment de la dernière vérification
  useEffect(() => {
    const interval = setInterval(() => {
      setLastCheck(new Date().toISOString());
    }, 30000); // Mettre à jour toutes les 30 secondes

    return () => clearInterval(interval);
  }, []);

  const handleDismissNotification = (alertId: number) => {
    setDismissedNotifications(prev => new Set([...prev, alertId]));
  };

  const handleViewAlert = (alert: Alert) => {
    onAlertClick(alert);
    handleDismissNotification(alert.id);
  };

  const getAlertIcon = (level: string) => {
    switch (level) {
      case 'CRITICAL':
        return <ExclamationTriangleIcon className="w-5 h-5 text-red-600" />;
      case 'HIGH':
        return <BellAlertIcon className="w-5 h-5 text-orange-600" />;
      case 'MEDIUM':
        return <BellIcon className="w-5 h-5 text-yellow-600" />;
      case 'LOW':
        return <CheckCircleIcon className="w-5 h-5 text-green-600" />;
      default:
        return <ClockIcon className="w-5 h-5 text-gray-600" />;
    }
  };

  const getAlertColor = (level: string) => {
    switch (level) {
      case 'CRITICAL':
        return 'border-red-200 bg-red-50';
      case 'HIGH':
        return 'border-orange-200 bg-orange-50';
      case 'MEDIUM':
        return 'border-yellow-200 bg-yellow-50';
      case 'LOW':
        return 'border-green-200 bg-green-50';
      default:
        return 'border-gray-200 bg-gray-50';
    }
  };

  // Filtrer les notifications non rejetées
  const visibleRecentAlerts = recentAlerts?.results?.filter(
    alert => !dismissedNotifications.has(alert.id)
  ) || [];

  const visibleUnreadAlerts = unreadAlerts?.results?.filter(
    alert => !dismissedNotifications.has(alert.id)
  ) || [];

  return (
    <div className={`space-y-6 ${className}`}>
      {/* En-tête */}
      <Card className="p-6">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
              <BellIcon className="w-5 h-5 text-blue-600" />
            </div>
            <div>
              <h2 className="text-xl font-bold text-gray-900">Centre de notifications</h2>
              <p className="text-sm text-gray-600">
                Alertes en temps réel et notifications importantes
              </p>
            </div>
          </div>
          <Button
            onClick={() => refetch()}
            variant="outline"
            size="sm"
            className="flex items-center space-x-2"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
            <span>Actualiser</span>
          </Button>
        </div>

        {/* Statistiques rapides */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-blue-50 rounded-lg p-3">
            <div className="text-sm text-blue-600">Nouvelles alertes</div>
            <div className="text-xl font-bold text-blue-900">
              {visibleRecentAlerts.length}
            </div>
          </div>
          <div className="bg-orange-50 rounded-lg p-3">
            <div className="text-sm text-orange-600">Non lues</div>
            <div className="text-xl font-bold text-orange-900">
              {visibleUnreadAlerts.length}
            </div>
          </div>
          <div className="bg-green-50 rounded-lg p-3">
            <div className="text-sm text-green-600">Dernière vérification</div>
            <div className="text-sm font-medium text-green-900">
              {formatDistanceToNow(new Date(lastCheck), { 
                addSuffix: true, 
                locale: fr 
              })}
            </div>
          </div>
        </div>
      </Card>

      {/* Nouvelles alertes */}
      <Card className="overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">
            Nouvelles alertes ({visibleRecentAlerts.length})
          </h3>
        </div>

        {isLoading ? (
          <div className="flex justify-center py-8">
            <Loading />
          </div>
        ) : visibleRecentAlerts.length === 0 ? (
          <div className="text-center py-8">
            <BellIcon className="w-12 h-12 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Aucune nouvelle alerte
            </h3>
            <p className="text-gray-500">
              Toutes les alertes récentes ont été consultées.
            </p>
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            <AnimatePresence>
              {visibleRecentAlerts.map((alert, index) => (
                <motion.div
                  key={alert.id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 20 }}
                  transition={{ delay: index * 0.1 }}
                  className={`p-4 border-l-4 ${getAlertColor(alert.level)} hover:bg-opacity-75 transition-colors`}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex items-start space-x-3 flex-1">
                      <div className="flex-shrink-0 mt-1">
                        {getAlertIcon(alert.level)}
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2 mb-1">
                          <h4 className="text-sm font-medium text-gray-900 truncate">
                            #{alert.id} - {alert.name}
                          </h4>
                          <span className={`px-2 py-0.5 rounded-full text-xs font-medium ${
                            alert.level === 'CRITICAL' ? 'bg-red-100 text-red-800' :
                            alert.level === 'HIGH' ? 'bg-orange-100 text-orange-800' :
                            alert.level === 'MEDIUM' ? 'bg-yellow-100 text-yellow-800' :
                            'bg-green-100 text-green-800'
                          }`}>
                            {alert.level}
                          </span>
                        </div>
                        <p className="text-sm text-gray-600 mb-2">
                          {alert.message || `${alert.detection_info?.type || alert.alert_type} détecté`}
                        </p>
                        <div className="flex items-center space-x-4 text-xs text-gray-500">
                          <span>{alert.region_name}</span>
                          <span>•</span>
                          <span>
                            {formatDistanceToNow(new Date(alert.sent_at), { 
                              addSuffix: true, 
                              locale: fr 
                            })}
                          </span>
                          {alert.requires_immediate_action && (
                            <>
                              <span>•</span>
                              <span className="text-red-600 font-medium">
                                Action immédiate requise
                              </span>
                            </>
                          )}
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2 ml-4">
                      <button
                        onClick={() => handleViewAlert(alert)}
                        className="p-1 text-blue-600 hover:text-blue-800 hover:bg-blue-100 rounded-full transition-colors"
                        title="Voir détails"
                      >
                        <EyeIcon className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => handleDismissNotification(alert.id)}
                        className="p-1 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-full transition-colors"
                        title="Masquer"
                      >
                        <XMarkIcon className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                </motion.div>
              ))}
            </AnimatePresence>
          </div>
        )}
      </Card>

      {/* Alertes non lues */}
      {visibleUnreadAlerts.length > 0 && (
        <Card className="overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">
              Alertes non lues ({visibleUnreadAlerts.length})
            </h3>
          </div>
          <div className="divide-y divide-gray-200">
            {visibleUnreadAlerts.slice(0, 10).map((alert, index) => (
              <motion.div
                key={alert.id}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.05 }}
                className="p-4 hover:bg-gray-50 transition-colors"
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                    <div>
                      <h4 className="text-sm font-medium text-gray-900">
                        #{alert.id} - {alert.name}
                      </h4>
                      <p className="text-xs text-gray-600">
                        {alert.region_name} • {formatDistanceToNow(new Date(alert.sent_at), { 
                          addSuffix: true, 
                          locale: fr 
                        })}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className={`px-2 py-0.5 rounded-full text-xs font-medium ${
                      alert.level === 'CRITICAL' ? 'bg-red-100 text-red-800' :
                      alert.level === 'HIGH' ? 'bg-orange-100 text-orange-800' :
                      alert.level === 'MEDIUM' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-green-100 text-green-800'
                    }`}>
                      {alert.level}
                    </span>
                    <button
                      onClick={() => handleViewAlert(alert)}
                      className="p-1 text-blue-600 hover:text-blue-800 hover:bg-blue-100 rounded-full transition-colors"
                      title="Voir détails"
                    >
                      <EyeIcon className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </motion.div>
            ))}
            {visibleUnreadAlerts.length > 10 && (
              <div className="p-4 text-center text-sm text-gray-500">
                +{visibleUnreadAlerts.length - 10} autres alertes non lues
              </div>
            )}
          </div>
        </Card>
      )}

      {/* Instructions */}
      <Card className="p-4 bg-blue-50 border-blue-200">
        <div className="flex items-start space-x-3">
          <BellIcon className="w-5 h-5 text-blue-600 mt-0.5" />
          <div>
            <h4 className="font-medium text-blue-900 mb-1">
              Notifications en temps réel
            </h4>
            <p className="text-sm text-blue-700">
              Cette page se met à jour automatiquement toutes les 5 secondes pour afficher 
              les nouvelles alertes. Cliquez sur "Actualiser" pour forcer une mise à jour immédiate.
            </p>
          </div>
        </div>
      </Card>
    </div>
  );
};
