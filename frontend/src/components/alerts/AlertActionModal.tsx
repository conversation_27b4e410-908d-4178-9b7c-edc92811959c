import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  XMarkIcon, 
  CheckCircleIcon, 
  ShieldCheckIcon,
  UserPlusIcon,
  XCircleIcon,
  ExclamationTriangleIcon 
} from '@heroicons/react/24/outline';
import { Alert } from '../../services/alert.service';
import { useQuery } from '@tanstack/react-query';
import investigationService from '../../services/investigation.service';
import { Modal, Button, Loading } from '../ui';

interface AlertActionModalProps {
  isOpen: boolean;
  onClose: () => void;
  alert: Alert;
  actionType: 'acknowledge' | 'resolve' | 'assign' | 'dismiss';
  onSubmit: (data: any) => void;
  isLoading?: boolean;
}

export const AlertActionModal: React.FC<AlertActionModalProps> = ({
  isOpen,
  onClose,
  alert,
  actionType,
  onSubmit,
  isLoading = false,
}) => {
  const [notes, setNotes] = useState('');
  const [assignedTo, setAssignedTo] = useState<number | null>(null);

  // Récupération des agents disponibles pour assignation
  const { data: agentsData } = useQuery({
    queryKey: ['available-agents'],
    queryFn: () => investigationService.getAvailableAgents(),
    enabled: actionType === 'assign',
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    const data: any = { notes };
    if (actionType === 'assign' && assignedTo) {
      data.assignedTo = assignedTo;
    }
    
    onSubmit(data);
  };

  const handleClose = () => {
    setNotes('');
    setAssignedTo(null);
    onClose();
  };

  const getActionConfig = () => {
    switch (actionType) {
      case 'acknowledge':
        return {
          title: 'Accuser réception de l\'alerte',
          description: 'Confirmer que vous avez pris connaissance de cette alerte.',
          icon: CheckCircleIcon,
          color: 'text-green-600',
          bgColor: 'bg-green-100',
          buttonText: 'Accuser réception',
          buttonClass: 'bg-green-600 hover:bg-green-700',
        };
      case 'resolve':
        return {
          title: 'Résoudre l\'alerte',
          description: 'Marquer cette alerte comme résolue avec vos notes de résolution.',
          icon: ShieldCheckIcon,
          color: 'text-blue-600',
          bgColor: 'bg-blue-100',
          buttonText: 'Résoudre',
          buttonClass: 'bg-blue-600 hover:bg-blue-700',
        };
      case 'assign':
        return {
          title: 'Assigner l\'alerte',
          description: 'Assigner cette alerte à un agent pour traitement.',
          icon: UserPlusIcon,
          color: 'text-purple-600',
          bgColor: 'bg-purple-100',
          buttonText: 'Assigner',
          buttonClass: 'bg-purple-600 hover:bg-purple-700',
        };
      case 'dismiss':
        return {
          title: 'Rejeter l\'alerte',
          description: 'Marquer cette alerte comme non pertinente ou faux positif.',
          icon: XCircleIcon,
          color: 'text-red-600',
          bgColor: 'bg-red-100',
          buttonText: 'Rejeter',
          buttonClass: 'bg-red-600 hover:bg-red-700',
        };
      default:
        return {
          title: 'Action sur l\'alerte',
          description: '',
          icon: ExclamationTriangleIcon,
          color: 'text-gray-600',
          bgColor: 'bg-gray-100',
          buttonText: 'Confirmer',
          buttonClass: 'bg-gray-600 hover:bg-gray-700',
        };
    }
  };

  const config = getActionConfig();
  const IconComponent = config.icon;

  if (!isOpen) return null;

  return (
    <Modal isOpen={isOpen} onClose={handleClose} size="lg">
      <div className="p-6">
        {/* En-tête */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-3">
            <div className={`w-10 h-10 ${config.bgColor} rounded-full flex items-center justify-center`}>
              <IconComponent className={`w-5 h-5 ${config.color}`} />
            </div>
            <div>
              <h2 className="text-xl font-bold text-gray-900">
                {config.title}
              </h2>
              <p className="text-sm text-gray-600">
                Alerte #{alert.id} - {alert.name}
              </p>
            </div>
          </div>
          <button
            onClick={handleClose}
            className="p-2 hover:bg-gray-100 rounded-full transition-colors"
          >
            <XMarkIcon className="w-5 h-5 text-gray-500" />
          </button>
        </div>

        {/* Informations alerte */}
        <div className="bg-gray-50 rounded-lg p-4 mb-6">
          <h3 className="font-medium text-gray-900 mb-2">Détails de l'alerte</h3>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-gray-600">Type :</span>
              <span className="ml-2 font-medium">{alert.detection_info?.type || alert.alert_type}</span>
            </div>
            <div>
              <span className="text-gray-600">Niveau :</span>
              <span className="ml-2 font-medium">{alert.level}</span>
            </div>
            <div>
              <span className="text-gray-600">Région :</span>
              <span className="ml-2 font-medium">{alert.region_name}</span>
            </div>
            <div>
              <span className="text-gray-600">Date :</span>
              <span className="ml-2 font-medium">
                {new Date(alert.sent_at).toLocaleDateString('fr-FR')}
              </span>
            </div>
          </div>
          {alert.message && (
            <div className="mt-2">
              <span className="text-gray-600">Message :</span>
              <p className="mt-1 text-sm text-gray-800">{alert.message}</p>
            </div>
          )}
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Sélection agent pour assignation */}
          {actionType === 'assign' && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">
                Sélectionner un agent *
              </label>
              {agentsData ? (
                <div className="space-y-2 max-h-64 overflow-y-auto">
                  {agentsData.agents.map((agent) => (
                    <motion.div
                      key={agent.id}
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      className={`p-4 border rounded-lg cursor-pointer transition-all ${
                        assignedTo === agent.id
                          ? 'border-purple-500 bg-purple-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                      onClick={() => setAssignedTo(agent.id)}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <div className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center">
                            <span className="text-sm font-medium text-gray-700">
                              {agent.full_name.split(' ').map(n => n[0]).join('')}
                            </span>
                          </div>
                          <div>
                            <h4 className="font-medium text-gray-900">{agent.full_name}</h4>
                            <p className="text-sm text-gray-600">{agent.identifier}</p>
                          </div>
                        </div>
                        <div className="text-right">
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                            agent.total_workload >= 8 
                              ? 'bg-red-100 text-red-800'
                              : agent.total_workload >= 5
                              ? 'bg-orange-100 text-orange-800'
                              : 'bg-green-100 text-green-800'
                          }`}>
                            {agent.total_workload >= 8 ? 'Surchargé' : 
                             agent.total_workload >= 5 ? 'Occupé' : 'Disponible'}
                          </span>
                          <p className="text-xs text-gray-500 mt-1">
                            {agent.total_workload} tâche{agent.total_workload > 1 ? 's' : ''}
                          </p>
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </div>
              ) : (
                <div className="flex justify-center py-4">
                  <Loading size="sm" />
                </div>
              )}
            </div>
          )}

          {/* Notes */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              {actionType === 'resolve' ? 'Notes de résolution *' : 'Notes (optionnel)'}
            </label>
            <textarea
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              rows={4}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
              placeholder={
                actionType === 'acknowledge' ? 'Commentaires sur la prise en charge...' :
                actionType === 'resolve' ? 'Décrivez comment l\'alerte a été résolue...' :
                actionType === 'assign' ? 'Instructions spéciales pour l\'agent...' :
                actionType === 'dismiss' ? 'Raison du rejet de l\'alerte...' :
                'Vos commentaires...'
              }
              required={actionType === 'resolve'}
            />
          </div>

          {/* Avertissement pour actions critiques */}
          {(actionType === 'dismiss' || (actionType === 'resolve' && alert.level === 'CRITICAL')) && (
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <div className="flex items-start space-x-2">
                <ExclamationTriangleIcon className="w-5 h-5 text-yellow-600 mt-0.5" />
                <div>
                  <h4 className="font-medium text-yellow-900">Attention</h4>
                  <p className="text-sm text-yellow-700 mt-1">
                    {actionType === 'dismiss' 
                      ? 'Cette action marquera définitivement l\'alerte comme non pertinente.'
                      : 'Vous êtes sur le point de résoudre une alerte critique. Assurez-vous que toutes les mesures appropriées ont été prises.'
                    }
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Actions */}
          <div className="flex items-center justify-end space-x-3 pt-4 border-t border-gray-200">
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={isLoading}
            >
              Annuler
            </Button>
            <Button
              type="submit"
              disabled={
                isLoading || 
                (actionType === 'assign' && !assignedTo) ||
                (actionType === 'resolve' && !notes.trim())
              }
              className={`flex items-center space-x-2 text-white ${config.buttonClass}`}
            >
              {isLoading ? (
                <>
                  <Loading size="sm" />
                  <span>Traitement...</span>
                </>
              ) : (
                <>
                  <IconComponent className="w-4 h-4" />
                  <span>{config.buttonText}</span>
                </>
              )}
            </Button>
          </div>
        </form>
      </div>
    </Modal>
  );
};
