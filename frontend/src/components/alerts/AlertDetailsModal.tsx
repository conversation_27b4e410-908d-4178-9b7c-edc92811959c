import React from 'react';
import { motion } from 'framer-motion';
import {
  XMarkIcon,
  ExclamationTriangleIcon,
  MapPinIcon,
  CalendarIcon,
  UserIcon,
  ClockIcon,
  CheckCircleIcon,
  ShieldCheckIcon,
  UserPlusIcon,
  XCircleIcon,
} from '@heroicons/react/24/outline';
import { Alert } from '../../services/alert.service';
import { Modal, Button } from '../ui';

interface AlertDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  alert: Alert;
  onAction: (alert: Alert, action: 'acknowledge' | 'resolve' | 'assign' | 'dismiss') => void;
}

export const AlertDetailsModal: React.FC<AlertDetailsModalProps> = ({
  isOpen,
  onClose,
  alert,
  onAction,
}) => {
  if (!isOpen) return null;

  const getLevelColor = (level: string) => {
    switch (level) {
      case 'CRITICAL':
        return 'text-red-600 bg-red-100';
      case 'HIGH':
        return 'text-orange-600 bg-orange-100';
      case 'MEDIUM':
        return 'text-yellow-600 bg-yellow-100';
      case 'LOW':
        return 'text-green-600 bg-green-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ACTIVE':
        return 'text-red-600 bg-red-100';
      case 'ACKNOWLEDGED':
        return 'text-blue-600 bg-blue-100';
      case 'RESOLVED':
        return 'text-green-600 bg-green-100';
      case 'DISMISSED':
        return 'text-gray-600 bg-gray-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const canPerformAction = (action: string) => {
    switch (action) {
      case 'acknowledge':
        return alert.alert_status === 'ACTIVE';
      case 'resolve':
      case 'assign':
        return alert.alert_status === 'ACTIVE' || alert.alert_status === 'ACKNOWLEDGED';
      case 'dismiss':
        return alert.alert_status !== 'RESOLVED';
      default:
        return false;
    }
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="xl">
      <div className="p-6">
        {/* En-tête */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center">
              <ExclamationTriangleIcon className="w-6 h-6 text-red-600" />
            </div>
            <div>
              <h2 className="text-xl font-bold text-gray-900">
                Alerte #{alert.id}
              </h2>
              <p className="text-sm text-gray-600">{alert.name}</p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-full transition-colors"
          >
            <XMarkIcon className="w-5 h-5 text-gray-500" />
          </button>
        </div>

        {/* Badges niveau et statut */}
        <div className="flex items-center space-x-3 mb-6">
          <span className={`px-3 py-1 rounded-full text-sm font-medium ${getLevelColor(alert.level)}`}>
            Niveau {alert.level}
          </span>
          <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(alert.alert_status)}`}>
            {alert.alert_status}
          </span>
          {alert.requires_immediate_action && (
            <span className="px-3 py-1 rounded-full text-sm font-medium bg-red-600 text-white">
              Action immédiate requise
            </span>
          )}
        </div>

        {/* Contenu principal */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
          {/* Informations générales */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900">Informations générales</h3>
            
            <div className="bg-gray-50 rounded-lg p-4 space-y-3">
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-600">Type d'alerte :</span>
                <span className="font-medium">{alert.alert_type}</span>
              </div>
              
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-600">Score de priorité :</span>
                <span className="font-medium">{alert.priority_score}/100</span>
              </div>
              
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-600">Région :</span>
                <span className="font-medium">{alert.region_name}</span>
              </div>
              
              <div className="text-sm">
                <span className="text-gray-600">Coordonnées :</span>
                <div className="flex items-center mt-1">
                  <MapPinIcon className="w-4 h-4 mr-1 text-gray-500" />
                  <span className="font-mono text-sm">{alert.detection_info?.coordinates}</span>
                </div>
              </div>
            </div>

            {/* Message d'alerte */}
            {alert.message && (
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Message</h4>
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                  <p className="text-sm text-blue-800">{alert.message}</p>
                </div>
              </div>
            )}
          </div>

          {/* Informations détection */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900">Détection associée</h3>
            
            {alert.detection_info ? (
              <div className="bg-gray-50 rounded-lg p-4 space-y-3">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">Type de détection :</span>
                  <span className="font-medium">{alert.detection_info.type}</span>
                </div>
                
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">Score de confiance :</span>
                  <span className="font-medium">
                    {Math.round(alert.detection_info.confidence_score * 100)}%
                  </span>
                </div>
                
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">Surface affectée :</span>
                  <span className="font-medium">{alert.detection_info.area_hectares.toFixed(2)} ha</span>
                </div>
                
                <div className="text-sm">
                  <span className="text-gray-600">Position GPS :</span>
                  <div className="mt-1 font-mono text-xs">
                    Lat: {alert.detection_info.latitude?.toFixed(6)}<br />
                    Lng: {alert.detection_info.longitude?.toFixed(6)}
                  </div>
                </div>
              </div>
            ) : (
              <div className="text-center py-4 text-gray-500">
                Aucune détection associée
              </div>
            )}

            {/* Investigation liée */}
            {alert.related_investigation_id && (
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Investigation liée</h4>
                <div className="bg-purple-50 border border-purple-200 rounded-lg p-3">
                  <p className="text-sm text-purple-800">
                    Investigation #{alert.related_investigation_id} créée pour cette alerte
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Informations temporelles et assignation */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
          {/* Chronologie */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Chronologie</h3>
            <div className="space-y-3">
              <div className="flex items-center space-x-3">
                <CalendarIcon className="w-5 h-5 text-gray-400" />
                <div>
                  <div className="text-sm font-medium text-gray-900">Alerte créée</div>
                  <div className="text-xs text-gray-500">
                    {new Date(alert.sent_at).toLocaleString('fr-FR')}
                  </div>
                </div>
              </div>
              
              {alert.acknowledged_at && (
                <div className="flex items-center space-x-3">
                  <CheckCircleIcon className="w-5 h-5 text-blue-500" />
                  <div>
                    <div className="text-sm font-medium text-gray-900">Accusé de réception</div>
                    <div className="text-xs text-gray-500">
                      {new Date(alert.acknowledged_at).toLocaleString('fr-FR')}
                      {alert.acknowledged_by_name && ` par ${alert.acknowledged_by_name}`}
                    </div>
                  </div>
                </div>
              )}
              
              {alert.resolved_at && (
                <div className="flex items-center space-x-3">
                  <ShieldCheckIcon className="w-5 h-5 text-green-500" />
                  <div>
                    <div className="text-sm font-medium text-gray-900">Résolue</div>
                    <div className="text-xs text-gray-500">
                      {new Date(alert.resolved_at).toLocaleString('fr-FR')}
                      {alert.resolved_by_name && ` par ${alert.resolved_by_name}`}
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Assignation */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Assignation</h3>
            {alert.assigned_to_name ? (
              <div className="bg-gray-50 rounded-lg p-4">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center">
                    <UserIcon className="w-5 h-5 text-gray-600" />
                  </div>
                  <div>
                    <div className="font-medium text-gray-900">{alert.assigned_to_name}</div>
                    <div className="text-sm text-gray-600">Agent assigné</div>
                  </div>
                </div>
              </div>
            ) : (
              <div className="text-center py-4 text-gray-500">
                <UserIcon className="w-8 h-8 mx-auto mb-2 text-gray-300" />
                <p>Aucun agent assigné</p>
              </div>
            )}
          </div>
        </div>

        {/* Actions */}
        <div className="flex items-center justify-end space-x-3 pt-4 border-t border-gray-200">
          <Button variant="outline" onClick={onClose}>
            Fermer
          </Button>
          
          {/* Actions contextuelles */}
          {canPerformAction('acknowledge') && (
            <Button
              onClick={() => onAction(alert, 'acknowledge')}
              className="flex items-center space-x-2 bg-green-600 hover:bg-green-700 text-white"
            >
              <CheckCircleIcon className="w-4 h-4" />
              <span>Accuser réception</span>
            </Button>
          )}
          
          {canPerformAction('assign') && (
            <Button
              onClick={() => onAction(alert, 'assign')}
              className="flex items-center space-x-2 bg-purple-600 hover:bg-purple-700 text-white"
            >
              <UserPlusIcon className="w-4 h-4" />
              <span>Assigner</span>
            </Button>
          )}
          
          {canPerformAction('resolve') && (
            <Button
              onClick={() => onAction(alert, 'resolve')}
              className="flex items-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white"
            >
              <ShieldCheckIcon className="w-4 h-4" />
              <span>Résoudre</span>
            </Button>
          )}
        </div>
      </div>
    </Modal>
  );
};
