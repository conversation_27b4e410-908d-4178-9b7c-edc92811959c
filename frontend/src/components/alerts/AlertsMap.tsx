import React, { useEffect, useRef } from 'react';
import L from 'leaflet';
import 'leaflet/dist/leaflet.css';
import { Alert } from '../../services/alert.service';
import { Card } from '../ui';

// Fix pour les icônes Leaflet
delete (L.Icon.Default.prototype as any)._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
});

interface AlertsByRegion {
  region: string;
  coordinates: [number, number];
  alerts: Alert[];
  count_by_level: {
    CRITICAL: number;
    HIGH: number;
    MEDIUM: number;
    LOW: number;
  };
}

interface AlertsMapProps {
  alertsByRegion: AlertsByRegion[];
  onAlertClick: (alert: Alert) => void;
  className?: string;
}

export const AlertsMap: React.FC<AlertsMapProps> = ({
  alertsByRegion,
  onAlertClick,
  className = '',
}) => {
  const mapRef = useRef<HTMLDivElement>(null);
  const mapInstanceRef = useRef<L.Map | null>(null);

  useEffect(() => {
    if (!mapRef.current) return;

    // Initialiser la carte
    if (!mapInstanceRef.current) {
      mapInstanceRef.current = L.map(mapRef.current).setView([8.0402, -2.8000], 9);

      // Ajouter les tuiles OpenStreetMap
      L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '© OpenStreetMap contributors',
        maxZoom: 18,
      }).addTo(mapInstanceRef.current);
    }

    // Nettoyer les markers existants
    mapInstanceRef.current.eachLayer((layer) => {
      if (layer instanceof L.Marker || layer instanceof L.CircleMarker) {
        mapInstanceRef.current!.removeLayer(layer);
      }
    });

    // Ajouter les markers pour chaque région
    alertsByRegion.forEach((regionData) => {
      const { region, coordinates, alerts, count_by_level } = regionData;
      
      // Calculer la taille du marker selon le nombre total d'alertes
      const totalAlerts = alerts.length;
      const radius = Math.max(10, Math.min(50, totalAlerts * 3));
      
      // Déterminer la couleur selon le niveau le plus critique
      let color = '#10B981'; // Vert par défaut (LOW)
      if (count_by_level.CRITICAL > 0) {
        color = '#DC2626'; // Rouge pour critique
      } else if (count_by_level.HIGH > 0) {
        color = '#EA580C'; // Orange pour élevé
      } else if (count_by_level.MEDIUM > 0) {
        color = '#D97706'; // Jaune pour moyen
      }

      // Créer le marker circulaire
      const marker = L.circleMarker(coordinates, {
        radius: radius,
        fillColor: color,
        color: '#ffffff',
        weight: 2,
        opacity: 1,
        fillOpacity: 0.7,
      }).addTo(mapInstanceRef.current!);

      // Créer le contenu du popup
      const popupContent = `
        <div class="p-3 min-w-64">
          <h3 class="font-bold text-lg mb-2">${region}</h3>
          <div class="space-y-2 mb-3">
            <div class="flex justify-between">
              <span class="text-gray-600">Total alertes:</span>
              <span class="font-semibold">${totalAlerts}</span>
            </div>
            ${count_by_level.CRITICAL > 0 ? `
              <div class="flex justify-between">
                <span class="text-red-600">Critiques:</span>
                <span class="font-semibold text-red-600">${count_by_level.CRITICAL}</span>
              </div>
            ` : ''}
            ${count_by_level.HIGH > 0 ? `
              <div class="flex justify-between">
                <span class="text-orange-600">Élevées:</span>
                <span class="font-semibold text-orange-600">${count_by_level.HIGH}</span>
              </div>
            ` : ''}
            ${count_by_level.MEDIUM > 0 ? `
              <div class="flex justify-between">
                <span class="text-yellow-600">Moyennes:</span>
                <span class="font-semibold text-yellow-600">${count_by_level.MEDIUM}</span>
              </div>
            ` : ''}
            ${count_by_level.LOW > 0 ? `
              <div class="flex justify-between">
                <span class="text-green-600">Faibles:</span>
                <span class="font-semibold text-green-600">${count_by_level.LOW}</span>
              </div>
            ` : ''}
          </div>
          <div class="border-t pt-2">
            <h4 class="font-medium mb-2">Alertes récentes:</h4>
            <div class="space-y-1 max-h-32 overflow-y-auto">
              ${alerts.slice(0, 5).map(alert => `
                <div class="text-sm p-2 bg-gray-50 rounded cursor-pointer hover:bg-gray-100" 
                     onclick="window.selectAlert(${alert.id})">
                  <div class="font-medium">#${alert.id} - ${alert.name}</div>
                  <div class="text-gray-600">${alert.level} • ${new Date(alert.sent_at).toLocaleDateString('fr-FR')}</div>
                </div>
              `).join('')}
              ${alerts.length > 5 ? `
                <div class="text-xs text-gray-500 text-center pt-1">
                  +${alerts.length - 5} autres alertes
                </div>
              ` : ''}
            </div>
          </div>
        </div>
      `;

      marker.bindPopup(popupContent, {
        maxWidth: 300,
        className: 'alerts-popup'
      });

      // Gestionnaire de clic sur le marker
      marker.on('click', () => {
        marker.openPopup();
      });
    });

    // Fonction globale pour sélection d'alerte depuis popup
    (window as any).selectAlert = (alertId: number) => {
      const alert = alertsByRegion
        .flatMap(region => region.alerts)
        .find(a => a.id === alertId);
      if (alert) {
        onAlertClick(alert);
      }
    };

    // Ajuster la vue pour inclure tous les markers
    if (alertsByRegion.length > 0) {
      const group = new L.FeatureGroup(
        alertsByRegion.map(region => 
          L.circleMarker(region.coordinates)
        )
      );
      mapInstanceRef.current.fitBounds(group.getBounds().pad(0.1));
    }

    return () => {
      if (mapInstanceRef.current) {
        mapInstanceRef.current.remove();
        mapInstanceRef.current = null;
      }
    };
  }, [alertsByRegion, onAlertClick]);

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Légende */}
      <Card className="p-4">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Carte des alertes par région</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <h4 className="font-medium text-gray-900 mb-2">Niveaux de criticité</h4>
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <div className="w-4 h-4 rounded-full bg-red-600"></div>
                <span className="text-sm">Critique</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-4 h-4 rounded-full bg-orange-600"></div>
                <span className="text-sm">Élevé</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-4 h-4 rounded-full bg-yellow-600"></div>
                <span className="text-sm">Moyen</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-4 h-4 rounded-full bg-green-600"></div>
                <span className="text-sm">Faible</span>
              </div>
            </div>
          </div>
          <div>
            <h4 className="font-medium text-gray-900 mb-2">Taille des markers</h4>
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 rounded-full bg-gray-400"></div>
                <span className="text-sm">1-3 alertes</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-4 h-4 rounded-full bg-gray-400"></div>
                <span className="text-sm">4-6 alertes</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-5 h-5 rounded-full bg-gray-400"></div>
                <span className="text-sm">7+ alertes</span>
              </div>
            </div>
          </div>
        </div>
        <p className="text-sm text-gray-600 mt-3">
          Cliquez sur un marker pour voir les détails des alertes de la région.
        </p>
      </Card>

      {/* Carte */}
      <Card className="overflow-hidden">
        <div ref={mapRef} className="w-full h-96" />
      </Card>

      {/* Statistiques globales */}
      {alertsByRegion.length > 0 && (
        <Card className="p-4">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Statistiques globales</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900">
                {alertsByRegion.reduce((sum, region) => sum + region.alerts.length, 0)}
              </div>
              <div className="text-sm text-gray-600">Total alertes</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600">
                {alertsByRegion.reduce((sum, region) => sum + region.count_by_level.CRITICAL, 0)}
              </div>
              <div className="text-sm text-gray-600">Critiques</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">
                {alertsByRegion.reduce((sum, region) => sum + region.count_by_level.HIGH, 0)}
              </div>
              <div className="text-sm text-gray-600">Élevées</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-600">
                {alertsByRegion.length}
              </div>
              <div className="text-sm text-gray-600">Régions affectées</div>
            </div>
          </div>
        </Card>
      )}

      {/* Message si aucune alerte */}
      {alertsByRegion.length === 0 && (
        <Card className="p-12 text-center">
          <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Aucune alerte géolocalisée
          </h3>
          <p className="text-gray-500">
            Aucune alerte n'est actuellement disponible pour affichage sur la carte.
          </p>
        </Card>
      )}
    </div>
  );
};
