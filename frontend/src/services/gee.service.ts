import axios from 'axios';
import { authService } from './auth.service';

const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:8000/api';

export interface SpectralMapsData {
  image_id: number;
  image_name: string;
  capture_date: string;
  region: string;
  spectral_maps: {
    ndvi_map_url: string;
    ndwi_map_url: string;
    ndti_map_url: string;
    bounds: {
      north: number;
      south: number;
      east: number;
      west: number;
    };
  };
  indices_data: {
    ndvi: any;
    ndwi: any;
    ndti: any;
  };
}

export interface AnalysisRequest {
  months_back?: number;
  region_bounds?: {
    north: number;
    south: number;
    east: number;
    west: number;
  };
}

export interface AnalysisResult {
  success: boolean;
  message: string;
  data?: {
    images_processed: number;
    detections_found: number;
    alerts_generated: number;
    investigations_created: number;
    analysis_date: string;
  };
  errors?: string[];
}

export interface IndicesData {
  image_id: number;
  ndvi_data: any;
  ndwi_data: any;
  ndti_data: any;
  ndvi_mean: number;
  ndwi_mean: number;
  ndti_mean: number;
  processing_status: string;
  processed_at: string;
}

export interface TrendsData {
  region_id: number;
  period: {
    start: string;
    end: string;
  };
  trends: Array<{
    date: string;
    ndvi_mean: number;
    ndwi_mean: number;
    ndti_mean: number;
    image_id: number;
  }>;
}

class GEEService {
  private getHeaders() {
    const token = authService.getAccessToken();
    return {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json',
    };
  }

  // Lancer une analyse complète
  async runAnalysis(request: AnalysisRequest): Promise<AnalysisResult> {
    try {
      const response = await axios.post(
        `${API_URL}/analysis/run/`,
        request,
        { headers: this.getHeaders() }
      );
      return response.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.error || 
        error.response?.data?.message || 
        'Erreur lors du lancement de l\'analyse'
      );
    }
  }

  // Récupérer les cartes spectrales pour une image
  async getSpectralMaps(imageId: number): Promise<SpectralMapsData> {
    try {
      const response = await axios.get(
        `${API_URL}/spectral/maps/${imageId}/`,
        { headers: this.getHeaders() }
      );
      return response.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.error || 
        'Erreur lors de la récupération des cartes spectrales'
      );
    }
  }

  // Récupérer les données d'indices spectraux
  async getIndicesData(imageId: number): Promise<IndicesData> {
    try {
      const response = await axios.get(
        `${API_URL}/spectral/indices/${imageId}/`,
        { headers: this.getHeaders() }
      );
      return response.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.error || 
        'Erreur lors de la récupération des indices spectraux'
      );
    }
  }

  // Récupérer les tendances d'indices pour une région
  async getIndicesTrends(regionId: number): Promise<TrendsData> {
    try {
      const response = await axios.get(
        `${API_URL}/spectral/trends/${regionId}/`,
        { headers: this.getHeaders() }
      );
      return response.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.error || 
        'Erreur lors de la récupération des tendances'
      );
    }
  }

  // Récupérer les images disponibles
  async getAvailableImages(): Promise<any[]> {
    try {
      const response = await axios.get(
        `${API_URL}/images/`,
        { headers: this.getHeaders() }
      );
      return response.data.results || response.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.error || 
        'Erreur lors de la récupération des images'
      );
    }
  }

  // Récupérer une image spécifique
  async getImage(imageId: number): Promise<any> {
    try {
      const response = await axios.get(
        `${API_URL}/images/${imageId}/`,
        { headers: this.getHeaders() }
      );
      return response.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.error || 
        'Erreur lors de la récupération de l\'image'
      );
    }
  }

  // Utilitaires pour les coordonnées
  static formatBounds(bounds: { north: number; south: number; east: number; west: number }) {
    return {
      north: Number(bounds.north.toFixed(6)),
      south: Number(bounds.south.toFixed(6)),
      east: Number(bounds.east.toFixed(6)),
      west: Number(bounds.west.toFixed(6)),
    };
  }

  static isValidBounds(bounds: any): bounds is { north: number; south: number; east: number; west: number } {
    return (
      bounds &&
      typeof bounds.north === 'number' &&
      typeof bounds.south === 'number' &&
      typeof bounds.east === 'number' &&
      typeof bounds.west === 'number' &&
      bounds.north > bounds.south &&
      bounds.east > bounds.west
    );
  }

  // Constantes pour la région de Bondoukou
  static readonly BONDOUKOU_BOUNDS = {
    north: 8.5,
    south: 7.5,
    east: -2.5,
    west: -3.5,
  };

  static readonly BONDOUKOU_CENTER = {
    lat: 8.0,
    lng: -3.0,
  };
}

export const geeService = new GEEService();
export default geeService;
