import axiosInstance from './axios.config';

const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:8000/api';

export interface DashboardStats {
  total_detections: number;
  active_alerts: number;
  pending_investigations: number;
  total_financial_risk: number;
  analysis_period_days: number;
  last_analysis_date: string;
  accuracy_rate: number;
  high_confidence_detections: number;
  detections_trend: Array<{
    date: string;
    count: number;
  }>;
  alerts_by_level: Record<string, number>;
  affected_zones: Array<{
    zone: string;
    detections: number;
  }>;
}

export interface ExecutiveSummary {
  period: string;
  key_metrics: {
    total_detections: number;
    critical_situations: number;
    estimated_financial_impact_fcfa: number;
    system_accuracy_percent: number;
    active_field_investigations: number;
  };
  recommendations: string[];
  next_actions: string[];
}

export interface DetectionTrends {
  period_days: number;
  daily_detections: Array<{
    day: string;
    count: number;
  }>;
  average_confidence: number;
  high_confidence_count: number;
  trend_analysis: string;
}

export interface FinancialImpact {
  total_estimated_loss_fcfa: number;
  total_affected_area_hectares: number;
  average_cost_per_hectare_fcfa: number;
  breakdown_by_risk_level: Array<{
    risk_level: string;
    count: number;
    total_amount: number;
    avg_amount: number;
  }>;
  economic_context: {
    ministry_annual_estimate_fcfa: number;
    our_detection_percentage: number;
  };
}

class StatsService {
  async getDashboardStats(): Promise<DashboardStats> {
    try {
      const response = await axiosInstance.get('/stats/dashboard/');
      return response.data;
    } catch (error) {
      console.error('Erreur récupération stats dashboard:', error);
      throw new Error('Impossible de récupérer les statistiques du dashboard');
    }
  }

  async getExecutiveSummary(): Promise<ExecutiveSummary> {
    try {
      const response = await axiosInstance.get('/stats/summary/');
      return response.data;
    } catch (error) {
      console.error('Erreur récupération résumé exécutif:', error);
      throw new Error('Impossible de récupérer le résumé exécutif');
    }
  }

  async getDetectionTrends(days: number = 30): Promise<DetectionTrends> {
    try {
      const response = await axiosInstance.get('/stats/detection-trends/', {
        params: { days },
      });
      return response.data;
    } catch (error) {
      console.error('Erreur récupération tendances détections:', error);
      throw new Error('Impossible de récupérer les tendances de détection');
    }
  }

  async getFinancialImpact(): Promise<FinancialImpact> {
    try {
      const response = await axiosInstance.get('/stats/financial-impact/');
      return response.data;
    } catch (error) {
      console.error('Erreur récupération impact financier:', error);
      throw new Error('Impossible de récupérer l\'impact financier');
    }
  }
}

export default new StatsService();