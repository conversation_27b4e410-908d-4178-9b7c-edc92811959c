import axios from 'axios';
import { authService } from './auth.service';
import toast from 'react-hot-toast';

const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:8000/api';

const axiosInstance = axios.create({
  baseURL: API_URL,
  timeout: 30000, // 30 secondes
  headers: {
    'Content-Type': 'application/json',
  },
});

// Intercepteur pour ajouter le token
axiosInstance.interceptors.request.use(
  (config) => {
    const token = authService.getAccessToken();
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Intercepteur pour gérer les erreurs et le rafraîchissement du token
axiosInstance.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;

    // Gestion des erreurs réseau
    if (!error.response) {
      toast.error('Erreur de connexion au serveur');
      return Promise.reject(error);
    }

    // Si erreur 401 et pas déjà en cours de rafraîchissement
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      try {
        // Rafraîchir le token
        const newToken = await authService.refreshToken();

        // Mettre à jour le header
        originalRequest.headers.Authorization = `Bearer ${newToken}`;

        // Réessayer la requête
        return axiosInstance(originalRequest);
      } catch (refreshError) {
        // Si échec du rafraîchissement, déconnexion
        authService.logout();
        toast.error('Session expirée, veuillez vous reconnecter');
        window.location.href = '/login';
        return Promise.reject(refreshError);
      }
    }

    // Gestion des autres erreurs HTTP
    if (error.response?.status >= 500) {
      toast.error('Erreur serveur, veuillez réessayer plus tard');
    } else if (error.response?.status === 403) {
      toast.error('Accès non autorisé');
    } else if (error.response?.status === 404) {
      toast.error('Ressource non trouvée');
    }

    return Promise.reject(error);
  }
);

export default axiosInstance;