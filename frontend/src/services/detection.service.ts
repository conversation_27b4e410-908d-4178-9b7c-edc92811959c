import axiosInstance from './axios.config';

export interface Detection {
  id: number;
  image: number;
  image_name: string;
  image_capture_date: string;
  region: number;
  region_name: string;
  latitude: number;
  longitude: number;
  detection_type: string;
  confidence_score: number;
  area_hectares: number;
  ndvi_anomaly_score: number;
  ndwi_anomaly_score: number;
  ndti_anomaly_score: number;
  validation_status: string;
  validated_by: number | null;
  validated_by_name: string | null;
  validated_at: string | null;
  detection_date: string;
  algorithm_version: string;
}

class DetectionService {
  async getDetections(params?: any): Promise<{ count: number; results: Detection[] }> {
    try {
      const response = await axiosInstance.get('/detections/', { params });
      return response.data;
    } catch (error) {
      console.error('Erreur récupération détections:', error);
      throw new Error('Impossible de récupérer les détections');
    }
  }

  async getDetection(id: number): Promise<Detection> {
    try {
      const response = await axiosInstance.get(`/detections/${id}/`);
      return response.data;
    } catch (error) {
      console.error('Erreur récupération détection:', error);
      throw new Error('Impossible de récupérer la détection');
    }
  }

  async getHighConfidenceDetections(): Promise<{ count: number; results: Detection[] }> {
    try {
      const response = await axiosInstance.get('/detections/high-confidence/');
      return response.data;
    } catch (error) {
      console.error('Erreur récupération détections haute confiance:', error);
      throw new Error('Impossible de récupérer les détections haute confiance');
    }
  }

  async validateDetection(id: number, validationStatus: string, comment?: string): Promise<{ message: string; data: Detection }> {
    try {
      const response = await axiosInstance.patch(`/detections/${id}/validate/`, {
        validation_status: validationStatus,
        comment: comment
      });
      return response.data;
    } catch (error) {
      console.error('Erreur validation détection:', error);
      throw new Error('Impossible de valider la détection');
    }
  }

  async deleteDetection(id: number): Promise<void> {
    try {
      await axiosInstance.delete(`/detections/${id}/`);
    } catch (error) {
      console.error('Erreur suppression détection:', error);
      throw new Error('Impossible de supprimer la détection');
    }
  }

  // Nouvelles méthodes pour les filtres avancés
  async getDetectionsByRegion(regionId: number): Promise<{ count: number; results: Detection[] }> {
    try {
      const response = await axiosInstance.get('/detections/', {
        params: { region: regionId },
      });
      return response.data;
    } catch (error) {
      console.error('Erreur récupération détections par région:', error);
      throw new Error('Impossible de récupérer les détections par région');
    }
  }

  async getDetectionsByType(detectionType: string): Promise<{ count: number; results: Detection[] }> {
    try {
      const response = await axiosInstance.get('/detections/', {
        params: { detection_type: detectionType },
      });
      return response.data;
    } catch (error) {
      console.error('Erreur récupération détections par type:', error);
      throw new Error('Impossible de récupérer les détections par type');
    }
  }

  async getDetectionsByStatus(validationStatus: string): Promise<{ count: number; results: Detection[] }> {
    try {
      const response = await axiosInstance.get('/detections/', {
        params: { validation_status: validationStatus },
      });
      return response.data;
    } catch (error) {
      console.error('Erreur récupération détections par statut:', error);
      throw new Error('Impossible de récupérer les détections par statut');
    }
  }

  // Méthodes pour les statistiques
  async getDetectionStats(): Promise<{
    total: number;
    by_type: Record<string, number>;
    by_status: Record<string, number>;
    by_confidence: Record<string, number>;
  }> {
    try {
      const response = await axiosInstance.get('/detections/stats/');
      return response.data;
    } catch (error) {
      console.error('Erreur récupération stats détections:', error);
      throw new Error('Impossible de récupérer les statistiques de détection');
    }
  }
}

export default new DetectionService();