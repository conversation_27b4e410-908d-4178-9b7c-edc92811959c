import axios from 'axios';
import { authService } from './auth.service';

const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:8000/api';

export interface Detection {
  id: number;
  image: number;
  image_name: string;
  image_capture_date: string;
  region: number;
  region_name: string;
  latitude: number;
  longitude: number;
  detection_type: string;
  confidence_score: number;
  area_hectares: number;
  ndvi_anomaly_score: number;
  ndwi_anomaly_score: number;
  ndti_anomaly_score: number;
  validation_status: string;
  validated_by: number | null;
  validated_by_name: string | null;
  validated_at: string | null;
  detection_date: string;
  algorithm_version: string;
}

class DetectionService {
  private getHeaders() {
    const token = authService.getAccessToken(); // Corrected method name
    return {
      Authorization: `Bearer ${token}`,
    };
  }

  async getDetections(params?: any): Promise<{ count: number; results: Detection[] }> {
    const response = await axios.get(`${API_URL}/detections/`, {
      headers: this.getHeaders(),
      params,
    });
    return response.data;
  }

  async getDetection(id: number): Promise<Detection> {
    const response = await axios.get(`${API_URL}/detections/${id}/`, {
      headers: this.getHeaders(),
    });
    return response.data;
  }

  async getHighConfidenceDetections(): Promise<{ count: number; results: Detection[] }> {
    const response = await axios.get(`${API_URL}/detections/high-confidence/`, {
      headers: this.getHeaders(),
    });
    return response.data;
  }

  async validateDetection(id: number, validationStatus: string, comment?: string): Promise<{ message: string; data: Detection }> {
    const response = await axios.patch(
      `${API_URL}/detections/${id}/validate/`,
      {
        validation_status: validationStatus,
        comment: comment
      },
      { headers: this.getHeaders() }
    );
    return response.data;
  }

  async deleteDetection(id: number): Promise<void> {
    await axios.delete(`${API_URL}/detections/${id}/`, {
      headers: this.getHeaders(),
    });
  }

  // Nouvelles méthodes pour les filtres avancés
  async getDetectionsByRegion(regionId: number): Promise<{ count: number; results: Detection[] }> {
    const response = await axios.get(`${API_URL}/detections/`, {
      headers: this.getHeaders(),
      params: { region: regionId },
    });
    return response.data;
  }

  async getDetectionsByType(detectionType: string): Promise<{ count: number; results: Detection[] }> {
    const response = await axios.get(`${API_URL}/detections/`, {
      headers: this.getHeaders(),
      params: { detection_type: detectionType },
    });
    return response.data;
  }

  async getDetectionsByStatus(validationStatus: string): Promise<{ count: number; results: Detection[] }> {
    const response = await axios.get(`${API_URL}/detections/`, {
      headers: this.getHeaders(),
      params: { validation_status: validationStatus },
    });
    return response.data;
  }

  // Méthodes pour les statistiques
  async getDetectionStats(): Promise<{
    total: number;
    by_type: Record<string, number>;
    by_status: Record<string, number>;
    by_confidence: Record<string, number>;
  }> {
    const response = await axios.get(`${API_URL}/detections/stats/`, {
      headers: this.getHeaders(),
    });
    return response.data;
  }
}

export default new DetectionService();