import axios from 'axios';
import { API_URL } from '../config';

// Types correspondant au backend
interface User {
  id: number;
  email: string;
  first_name: string;
  last_name: string;
  full_name: string;
  job_title: string;
  institution: string;
  authorized_region: string;
  authorities: string[];
  primary_authority: string;
  is_active: boolean;
}

interface LoginResponse {
  access_token: string;
  refresh_token: string;
  user: User;
  authorities: string[];
  expires_in: number;
}

interface ChangePasswordRequest {
  old_password: string;
  new_password: string;
}

interface UpdateProfileRequest {
  first_name?: string;
  last_name?: string;
  job_title?: string;
  institution?: string;
}

// Service
const TOKEN_KEY = 'auth_token';
const REFRESH_TOKEN_KEY = 'refresh_token';

class AuthService {
  async login(email: string, password: string): Promise<LoginResponse> {
    try {
      const response = await axios.post(`${API_URL}/auth/login/`, {
        email,
        password,
      });

      const { access_token, refresh_token, user, authorities, expires_in } = response.data;
      this.setTokens(access_token, refresh_token);

      return { access_token, refresh_token, user, authorities, expires_in };
    } catch (error: any) {
      if (error.response?.status === 401) {
        throw new Error('Identifiants incorrects');
      }
      throw new Error('Erreur de connexion');
    }
  }

  logout(): void {
    localStorage.removeItem(TOKEN_KEY);
    localStorage.removeItem(REFRESH_TOKEN_KEY);
  }

  setTokens(access: string, refresh: string): void {
    localStorage.setItem(TOKEN_KEY, access);
    localStorage.setItem(REFRESH_TOKEN_KEY, refresh);
  }

  getAccessToken(): string | null {
    return localStorage.getItem(TOKEN_KEY);
  }

  getRefreshToken(): string | null {
    return localStorage.getItem(REFRESH_TOKEN_KEY);
  }

  isAuthenticated(): boolean {
    return !!this.getAccessToken();
  }

  async refreshToken(): Promise<string> {
    try {
      const refresh = this.getRefreshToken();
      if (!refresh) {
        throw new Error('Pas de token de rafraîchissement');
      }

      const response = await axios.post(`${API_URL}/auth/refresh/`, {
        refresh_token: refresh,
      });

      const { access_token } = response.data;
      localStorage.setItem(TOKEN_KEY, access_token);
      return access_token;
    } catch (error) {
      this.logout();
      throw new Error('Session expirée');
    }
  }

  async getUserProfile(): Promise<User> {
    try {
      const response = await axios.get(`${API_URL}/auth/profile/`, {
        headers: {
          Authorization: `Bearer ${this.getAccessToken()}`,
        },
      });
      return response.data;
    } catch (error) {
      throw new Error('Erreur récupération profil');
    }
  }

  async updateProfile(data: UpdateProfileRequest): Promise<User> {
    try {
      const response = await axios.put(`${API_URL}/account/profile/`, data, {
        headers: {
          Authorization: `Bearer ${this.getAccessToken()}`,
        },
      });
      return response.data;
    } catch (error: any) {
      if (error.response?.data?.errors) {
        throw new Error(Object.values(error.response.data.errors).join(', '));
      }
      throw new Error('Erreur mise à jour profil');
    }
  }

  async changePassword(data: ChangePasswordRequest): Promise<void> {
    try {
      await axios.post(`${API_URL}/account/change_password/`, data, {
        headers: {
          Authorization: `Bearer ${this.getAccessToken()}`,
        },
      });
    } catch (error: any) {
      if (error.response?.data?.error) {
        throw new Error(error.response.data.error);
      }
      throw new Error('Erreur changement mot de passe');
    }
  }

  // Méthodes utilitaires pour les permissions
  hasAuthority(user: User | null, authorityName: string): boolean {
    if (!user || !user.authorities) return false;
    return user.authorities.includes(authorityName);
  }

  hasAnyAuthority(user: User | null, authorityNames: string[]): boolean {
    if (!user || !user.authorities) return false;
    return authorityNames.some(name => this.hasAuthority(user, name));
  }

  canViewStats(user: User | null): boolean {
    return this.hasAnyAuthority(user, [
      'Responsable Régional',
      'Agent Analyste',
      'Agent Technique',
      'Administrateur'
    ]);
  }

  canLaunchAnalysis(user: User | null): boolean {
    return this.hasAnyAuthority(user, [
      'Responsable Régional',
      'Agent Analyste',
      'Administrateur'
    ]);
  }

  canManageInvestigations(user: User | null): boolean {
    return this.hasAnyAuthority(user, [
      'Responsable Régional',
      'Administrateur'
    ]);
  }

  canViewFinancialRisks(user: User | null): boolean {
    return this.hasAnyAuthority(user, [
      'Responsable Régional',
      'Administrateur'
    ]);
  }

  isAdmin(user: User | null): boolean {
    return this.hasAuthority(user, 'Administrateur');
  }
}

export const authService = new AuthService();
export type { User, LoginResponse, ChangePasswordRequest, UpdateProfileRequest };