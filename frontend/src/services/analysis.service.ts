import axiosInstance from './axios.config';

// Types pour l'analyse spectrale selon spécifications DoD
export interface AnalysisZone {
  id?: number;
  name: string;
  coordinates: [number, number][];
  area_hectares: number;
  center_lat: number;
  center_lng: number;
  created_at?: string;
}

export interface AnalysisParameters {
  zone_id?: number;
  custom_zone?: AnalysisZone;
  start_date: string;
  end_date: string;
  cloud_coverage_max: number;
  detection_sensitivity: 'LOW' | 'MEDIUM' | 'HIGH';
  analysis_type: 'MINING_DETECTION' | 'CHANGE_DETECTION' | 'VEGETATION_ANALYSIS';
  satellite_sources: string[];
  spectral_indices: string[];
}

export interface AnalysisJob {
  id: string;
  name: string;
  status: 'PENDING' | 'RUNNING' | 'COMPLETED' | 'FAILED' | 'CANCELLED';
  progress: number;
  parameters: AnalysisParameters;
  created_at: string;
  started_at?: string;
  completed_at?: string;
  error_message?: string;
  results_url?: string;
  created_by: number;
  created_by_name: string;
}

export interface AnalysisResult {
  id: string;
  job_id: string;
  total_images_processed: number;
  detections_found: number;
  alerts_generated: number;
  investigations_created: number;
  analysis_date: string;
  processing_time_seconds: number;
  coverage_area_hectares: number;
  detection_summary: {
    mining_sites: number;
    water_pollution: number;
    deforestation: number;
    soil_disturbance: number;
  };
  confidence_distribution: {
    high: number;
    medium: number;
    low: number;
  };
  geojson_results?: any;
  thumbnail_url?: string;
  full_report_url?: string;
}

export interface SatelliteSource {
  id: string;
  name: string;
  description: string;
  resolution_meters: number;
  revisit_days: number;
  available: boolean;
  bands: string[];
}

export interface SpectralIndex {
  id: string;
  name: string;
  description: string;
  formula: string;
  use_case: string;
  bands_required: string[];
}

class AnalysisService {
  // Récupération des zones prédéfinies
  async getAnalysisZones(): Promise<AnalysisZone[]> {
    try {
      const response = await axiosInstance.get('/analysis/zones/');
      return response.data.results || response.data;
    } catch (error) {
      console.error('Erreur récupération zones analyse:', error);
      throw new Error('Impossible de récupérer les zones d\'analyse');
    }
  }

  // Création d'une zone personnalisée
  async createAnalysisZone(zone: Omit<AnalysisZone, 'id' | 'created_at'>): Promise<AnalysisZone> {
    try {
      const response = await axiosInstance.post('/analysis/zones/', zone);
      return response.data;
    } catch (error) {
      console.error('Erreur création zone analyse:', error);
      throw new Error('Impossible de créer la zone d\'analyse');
    }
  }

  // Récupération des sources satellites disponibles
  async getSatelliteSources(): Promise<SatelliteSource[]> {
    try {
      const response = await axiosInstance.get('/analysis/satellite-sources/');
      return response.data.results || response.data;
    } catch (error) {
      console.error('Erreur récupération sources satellites:', error);
      // Fallback avec sources par défaut
      return [
        {
          id: 'sentinel2',
          name: 'Sentinel-2',
          description: 'Images haute résolution (10m) avec 13 bandes spectrales',
          resolution_meters: 10,
          revisit_days: 5,
          available: true,
          bands: ['B2', 'B3', 'B4', 'B8', 'B11', 'B12'],
        },
        {
          id: 'landsat8',
          name: 'Landsat 8',
          description: 'Images moyennes résolution (30m) avec archive historique',
          resolution_meters: 30,
          revisit_days: 16,
          available: true,
          bands: ['B2', 'B3', 'B4', 'B5', 'B6', 'B7'],
        },
      ];
    }
  }

  // Récupération des indices spectraux disponibles
  async getSpectralIndices(): Promise<SpectralIndex[]> {
    try {
      const response = await axiosInstance.get('/analysis/spectral-indices/');
      return response.data.results || response.data;
    } catch (error) {
      console.error('Erreur récupération indices spectraux:', error);
      // Fallback avec indices par défaut
      return [
        {
          id: 'ndvi',
          name: 'NDVI',
          description: 'Indice de végétation normalisé',
          formula: '(NIR - Red) / (NIR + Red)',
          use_case: 'Détection déforestation',
          bands_required: ['B4', 'B8'],
        },
        {
          id: 'ndwi',
          name: 'NDWI',
          description: 'Indice d\'eau normalisé',
          formula: '(Green - NIR) / (Green + NIR)',
          use_case: 'Détection pollution hydrique',
          bands_required: ['B3', 'B8'],
        },
        {
          id: 'ndmi',
          name: 'NDMI',
          description: 'Indice d\'humidité normalisé',
          formula: '(NIR - SWIR1) / (NIR + SWIR1)',
          use_case: 'Détection perturbation sol',
          bands_required: ['B8', 'B11'],
        },
      ];
    }
  }

  // Lancement d'une nouvelle analyse
  async startAnalysis(parameters: AnalysisParameters, name: string): Promise<AnalysisJob> {
    try {
      const response = await axiosInstance.post('/analysis/jobs/', {
        name,
        parameters,
      });
      return response.data;
    } catch (error) {
      console.error('Erreur lancement analyse:', error);
      throw new Error('Impossible de lancer l\'analyse');
    }
  }

  // Récupération des jobs d'analyse
  async getAnalysisJobs(page: number = 1, pageSize: number = 20): Promise<{
    count: number;
    results: AnalysisJob[];
  }> {
    try {
      const response = await axiosInstance.get('/analysis/jobs/', {
        params: { page, page_size: pageSize },
      });
      return response.data;
    } catch (error) {
      console.error('Erreur récupération jobs analyse:', error);
      throw new Error('Impossible de récupérer les analyses');
    }
  }

  // Récupération d'un job spécifique
  async getAnalysisJob(jobId: string): Promise<AnalysisJob> {
    try {
      const response = await axiosInstance.get(`/analysis/jobs/${jobId}/`);
      return response.data;
    } catch (error) {
      console.error('Erreur récupération job analyse:', error);
      throw new Error('Impossible de récupérer l\'analyse');
    }
  }

  // Annulation d'un job
  async cancelAnalysisJob(jobId: string): Promise<{ success: boolean; message: string }> {
    try {
      const response = await axiosInstance.post(`/analysis/jobs/${jobId}/cancel/`);
      return response.data;
    } catch (error) {
      console.error('Erreur annulation job analyse:', error);
      throw new Error('Impossible d\'annuler l\'analyse');
    }
  }

  // Récupération des résultats d'analyse
  async getAnalysisResults(jobId: string): Promise<AnalysisResult> {
    try {
      const response = await axiosInstance.get(`/analysis/jobs/${jobId}/results/`);
      return response.data;
    } catch (error) {
      console.error('Erreur récupération résultats analyse:', error);
      throw new Error('Impossible de récupérer les résultats');
    }
  }

  // Téléchargement du rapport complet
  async downloadAnalysisReport(jobId: string): Promise<Blob> {
    try {
      const response = await axiosInstance.get(`/analysis/jobs/${jobId}/report/`, {
        responseType: 'blob',
      });
      return response.data;
    } catch (error) {
      console.error('Erreur téléchargement rapport:', error);
      throw new Error('Impossible de télécharger le rapport');
    }
  }

  // Récupération des jobs en cours (pour monitoring temps réel)
  async getRunningJobs(): Promise<AnalysisJob[]> {
    try {
      const response = await axiosInstance.get('/analysis/jobs/running/');
      return response.data.results || response.data;
    } catch (error) {
      console.error('Erreur récupération jobs en cours:', error);
      return [];
    }
  }

  // Validation des paramètres avant lancement
  async validateAnalysisParameters(parameters: AnalysisParameters): Promise<{
    valid: boolean;
    errors: string[];
    warnings: string[];
    estimated_processing_time: number;
    estimated_cost: number;
  }> {
    try {
      const response = await axiosInstance.post('/analysis/validate/', { parameters });
      return response.data;
    } catch (error) {
      console.error('Erreur validation paramètres:', error);
      // Validation côté client basique
      const errors: string[] = [];
      const warnings: string[] = [];

      if (!parameters.start_date || !parameters.end_date) {
        errors.push('Les dates de début et fin sont obligatoires');
      }

      if (new Date(parameters.start_date) >= new Date(parameters.end_date)) {
        errors.push('La date de début doit être antérieure à la date de fin');
      }

      if (parameters.satellite_sources.length === 0) {
        errors.push('Au moins une source satellite doit être sélectionnée');
      }

      if (parameters.cloud_coverage_max > 80) {
        warnings.push('Couverture nuageuse élevée - résultats potentiellement limités');
      }

      return {
        valid: errors.length === 0,
        errors,
        warnings,
        estimated_processing_time: 300, // 5 minutes par défaut
        estimated_cost: 0,
      };
    }
  }
}

export default new AnalysisService();