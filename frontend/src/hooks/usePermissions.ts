import { useAuth } from '../contexts/AuthContext';

export const usePermissions = () => {
  const {
    user,
    hasAuthority,
    hasRole,
    hasPermission,
    canViewStats,
    canLaunchAnalysis,
    canManageInvestigations,
    canViewFinancialRisks,
    isResponsableRegional,
    isAgentTerrain,
    isAdmin
  } = useAuth();

  return {
    // Permissions de base (délégation vers AuthContext)
    hasAuthority,
    hasRole,
    hasPermission,
    hasAnyAuthority: (authorityNames: string[]) =>
      authorityNames.some(name => hasAuthority(name)),

    // Permissions spécifiques (délégation vers AuthContext)
    canViewStats,
    canLaunchAnalysis,
    canManageInvestigations,
    canViewFinancialRisks,
    isAdmin,

    // Permissions par rôle (délégation vers AuthContext)
    isAdministrateur: () => hasAuthority('Administrateur'),
    isResponsableRegional,
    isAgentAnalyste: () => hasAuthority('Agent Analyste'),
    isAgentTechnique: () => hasAuthority('Agent Technique'),
    isAgentTerrain,

    // Permissions combinées
    canManageAlerts: () =>
      hasAuthority('Responsable Régional') || hasAuthority('Administrateur'),

    canValidateDetections: () =>
      hasAuthority('Agent Analyste') ||
      hasAuthority('Responsable Régional') ||
      hasAuthority('Administrateur'),

    canViewLogs: () =>
      hasAuthority('Agent Technique') ||
      hasAuthority('Responsable Régional') ||
      hasAuthority('Administrateur'),

    // Informations utilisateur
    user,
    primaryAuthority: user?.primary_authority,
    authorities: user?.authorities || [],
  };
};
