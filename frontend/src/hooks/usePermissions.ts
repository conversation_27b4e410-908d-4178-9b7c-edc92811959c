import { useAuth } from '../contexts/AuthContext';
import { authService } from '../services/auth.service';

export const usePermissions = () => {
  const { user } = useAuth();

  return {
    // Permissions de base
    hasAuthority: (authorityName: string) => authService.hasAuthority(user, authorityName),
    hasAnyAuthority: (authorityNames: string[]) => authService.hasAnyAuthority(user, authorityNames),
    
    // Permissions spécifiques
    canViewStats: () => authService.canViewStats(user),
    canLaunchAnalysis: () => authService.canLaunchAnalysis(user),
    canManageInvestigations: () => authService.canManageInvestigations(user),
    canViewFinancialRisks: () => authService.canViewFinancialRisks(user),
    isAdmin: () => authService.isAdmin(user),
    
    // Permissions par rôle
    isAdministrateur: () => authService.hasAuthority(user, 'Administrateur'),
    isResponsableRegional: () => authService.hasAuthority(user, 'Responsable Régional'),
    isAgentAnalyste: () => authService.hasAuthority(user, 'Agent Analyste'),
    isAgentTechnique: () => authService.hasAuthority(user, 'Agent Technique'),
    isAgentTerrain: () => authService.hasAuthority(user, 'Agent Terrain'),
    
    // Permissions combinées
    canManageAlerts: () => authService.hasAnyAuthority(user, [
      'Responsable Régional', 
      'Administrateur'
    ]),
    
    canValidateDetections: () => authService.hasAnyAuthority(user, [
      'Agent Analyste',
      'Responsable Régional', 
      'Administrateur'
    ]),
    
    canViewLogs: () => authService.hasAnyAuthority(user, [
      'Agent Technique',
      'Responsable Régional', 
      'Administrateur'
    ]),
    
    // Informations utilisateur
    user,
    primaryAuthority: user?.primary_authority,
    authorities: user?.authorities || [],
  };
};
