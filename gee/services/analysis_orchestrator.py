from datetime import datetime, timed<PERSON><PERSON>
from typing import List, Dict

from gee.services.earth_engine_service import EarthEngineService
from gee.services.mining_detection_service import MiningDetectionService

from report.services.event_log_service import EventLogService


class AnalysisOrchestrator:
    """Service orchestrateur pour analyses complètes"""

    def __init__(self):
        self.gee_service = EarthEngineService()
        self.detection_service = MiningDetectionService()

    def run_complete_analysis(self, months_back: int = 3, user_id: int = None) -> Dict:
        """
        Lance une analyse complète de détection d'orpaillage

        Args:
            months_back: Période d'analyse en mois
            user_id: ID utilisateur demandant l'analyse

        Returns:
            Résultats de l'analyse complète avec tous les objets créés
        """
        results = {
            'success': False,
            'images_processed': 0,
            'detections_found': 0,
            'alerts_generated': 0,
            'investigations_created': 0,
            'financial_risks_calculated': 0,
            'errors': [],
            # NOUVEAUX : Objets créés
            'created_detections': [],
            'created_alerts': [],
            'created_financial_risks': [],
            'created_investigations': []
        }

        try:
            # Récupération utilisateur pour logging
            user = None
            if user_id:
                from account.models.user_model import UserModel
                user = UserModel.objects.filter(id=user_id).first()

            # LOG DÉBUT ANALYSE
            EventLogService.log_analysis_started(user, months_back)
            print(f"DEBUG: Début analyse pour {months_back} mois")

            # 1. Récupération images récentes
            print("Récupération images satellites récentes...")
            recent_images = self.gee_service.get_recent_images(months_back)
            print(f"DEBUG: Récupéré {len(recent_images)} images")

            if not recent_images:
                error_msg = "Aucune image satellite trouvée"
                print(f"DEBUG: {error_msg}")
                results['errors'].append(error_msg)

                # Log erreur
                EventLogService.log_event(
                    'SYSTEM_ERROR',
                    error_msg,
                    user=user,
                    metadata={'months_back': months_back}
                )

                # Marquer comme succès même sans images (pas une erreur système)
                results['success'] = True
                return results

            print(f"Trouvé {len(recent_images)} images disponibles")

            # 2. Traitement de chaque image
            all_created_detections = []

            for img_data in recent_images[-5:]:  # Limite 5 images les plus récentes pour MVP
                print(f"Traitement image {img_data['gee_asset_id']}...")

                try:
                    # Traitement image complète
                    image_record = self.gee_service.process_image_complete(
                        img_data['gee_asset_id'],
                        user_id
                    )

                    if image_record and image_record.processing_status == 'COMPLETED':
                        results['images_processed'] += 1

                        # Log traitement image réussi
                        EventLogService.log_event(
                            'IMAGE_PROCESSED',
                            f"Image {image_record.name} traitée avec succès",
                            user=user,
                            metadata={
                                'image_id': image_record.id,
                                'gee_asset_id': img_data['gee_asset_id'],
                                'capture_date': image_record.capture_date.isoformat()
                            }
                        )

                        # Analyse détection orpaillage
                        detections = self.detection_service.analyze_for_mining_activity(image_record)
                        all_created_detections.extend(detections)

                        print(f"  → {len(detections)} détections trouvées")

                    else:
                        error_msg = f"Erreur traitement {img_data['gee_asset_id']}"
                        results['errors'].append(error_msg)
                        print(f"  → {error_msg}")

                        # Log erreur traitement image
                        EventLogService.log_event(
                            'SYSTEM_ERROR',
                            error_msg,
                            user=user,
                            metadata={
                                'gee_asset_id': img_data['gee_asset_id'],
                                'error_type': 'image_processing_failed'
                            }
                        )

                except Exception as e:
                    error_msg = f"Erreur traitement image {img_data['gee_asset_id']}: {str(e)}"
                    results['errors'].append(error_msg)
                    print(f"  → {error_msg}")

                    # Log erreur exception
                    EventLogService.log_event(
                        'SYSTEM_ERROR',
                        error_msg,
                        user=user,
                        metadata={
                            'gee_asset_id': img_data['gee_asset_id'],
                            'error_type': 'processing_exception',
                            'exception': str(e)
                        }
                    )

            # 3. Récupération de tous les objets associés créés
            created_alerts = []
            created_financial_risks = []
            created_investigations = []

            for detection in all_created_detections:
                # Récupérer les alertes associées
                alerts = detection.alerts.all()
                created_alerts.extend(alerts)

                # Récupérer le risque financier
                try:
                    financial_risk = detection.financial_risk
                    if financial_risk:
                        created_financial_risks.append(financial_risk)
                except:
                    pass

                # Récupérer les investigations
                try:
                    from detection.models.investigation_model import InvestigationModel
                    investigations = InvestigationModel.objects.filter(detection=detection)
                    created_investigations.extend(investigations)
                except:
                    pass

            # 4. Calcul résultats finaux
            results['success'] = True
            results['detections_found'] = len(all_created_detections)
            results['alerts_generated'] = len(created_alerts)
            results['financial_risks_calculated'] = len(created_financial_risks)
            results['investigations_created'] = len(created_investigations)

            # 5. Sérialisation des objets pour le frontend
            results['created_detections'] = self._serialize_detections(all_created_detections)
            results['created_alerts'] = self._serialize_alerts(created_alerts)
            results['created_financial_risks'] = self._serialize_financial_risks(created_financial_risks)
            results['created_investigations'] = self._serialize_investigations(created_investigations)

            print(f"Analyse terminée: {results['detections_found']} détections trouvées")

            # LOG FIN ANALYSE AVEC RÉSULTATS
            EventLogService.log_analysis_completed(user, {
                'images_processed': results['images_processed'],
                'detections_found': results['detections_found'],
                'alerts_generated': results['alerts_generated'],
                'investigations_created': results['investigations_created'],
                'financial_risks_calculated': results['financial_risks_calculated'],
                'errors_count': len(results['errors']),
                'success': results['success']
            })

            # Log détaillé des résultats
            if results['detections_found'] > 0:
                EventLogService.log_event(
                    'ANALYSIS_COMPLETED',
                    f"Analyse terminée avec succès: {results['detections_found']} détections, "
                    f"{results['alerts_generated']} alertes générées sur {results['images_processed']} images",
                    user=user,
                    metadata=results
                )

        except Exception as e:
            error_msg = f"Erreur analyse complète: {str(e)}"
            results['errors'].append(error_msg)
            results['success'] = False
            print(f"Erreur dans l'orchestrateur: {e}")

            # LOG ERREUR CRITIQUE
            EventLogService.log_event(
                'SYSTEM_ERROR',
                error_msg,
                user=user,
                metadata={
                    'months_back': months_back,
                    'error_type': 'analysis_orchestrator_failure',
                    'exception': str(e)
                }
            )

        return results

    def _serialize_detections(self, detections):
        """Sérialise les détections pour le frontend"""
        serialized = []
        for detection in detections:
            data = {
                'id': detection.id,
                'latitude': detection.latitude,
                'longitude': detection.longitude,
                'detection_type': detection.detection_type,
                'confidence_score': detection.confidence_score,
                'area_hectares': detection.area_hectares,
                'validation_status': detection.validation_status,
                'detection_date': detection.detection_date.isoformat(),
                'algorithm_version': detection.algorithm_version,
                'ndvi_anomaly_score': detection.ndvi_anomaly_score,
                'ndwi_anomaly_score': detection.ndwi_anomaly_score,
                'ndti_anomaly_score': detection.ndti_anomaly_score,
                'region_name': detection.region.name,
                'image_name': detection.image.name if detection.image else None
            }
            serialized.append(data)
        return serialized

    def _serialize_alerts(self, alerts):
        """Sérialise les alertes pour le frontend"""
        serialized = []
        for alert in alerts:
            data = {
                'id': alert.id,
                'name': alert.name,
                'detection_id': alert.detection.id,
                'region_name': alert.region.name,
                'level': alert.level,
                'alert_type': alert.alert_type,
                'message': alert.message,
                'alert_status': alert.alert_status,
                'sent_at': alert.sent_at.isoformat(),
                'is_read': alert.is_read,
                'assigned_to_name': alert.assigned_to.get_full_name() if alert.assigned_to else None
            }
            serialized.append(data)
        return serialized

    def _serialize_financial_risks(self, financial_risks):
        """Sérialise les risques financiers pour le frontend"""
        serialized = []
        for risk in financial_risks:
            data = {
                'id': risk.id,
                'detection_id': risk.detection.id,
                'area_hectares': risk.area_hectares,
                'cost_per_hectare': risk.cost_per_hectare,
                'estimated_loss': risk.estimated_loss,
                'risk_level': risk.risk_level,
                'sensitive_zone_distance_km': risk.sensitive_zone_distance_km,
                'occurrence_count': risk.occurrence_count,
                'created_at': risk.created_at.isoformat()
            }
            serialized.append(data)
        return serialized

    def _serialize_investigations(self, investigations):
        """Sérialise les investigations pour le frontend"""
        serialized = []
        for investigation in investigations:
            data = {
                'id': investigation.id,
                'detection_id': investigation.detection.id,
                'target_coordinates': investigation.target_coordinates,
                'access_instructions': investigation.access_instructions,
                'status': investigation.status,
                'assigned_to_name': investigation.assigned_to.get_full_name() if investigation.assigned_to else None,
                'result': investigation.result,
                'field_notes': investigation.field_notes,
                'investigation_date': investigation.investigation_date.isoformat() if investigation.investigation_date else None,
                'created_at': investigation.created_at.isoformat()
            }
            serialized.append(data)
        return serialized