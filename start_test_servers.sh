#!/bin/bash

# Script pour démarrer les serveurs de test GoldSentinel
# Usage: ./start_test_servers.sh

set -e

echo "🚀 Démarrage des serveurs de test GoldSentinel"
echo "=============================================="

# Couleurs pour l'affichage
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Fonction pour afficher les messages colorés
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Vérifier que nous sommes dans le bon répertoire
if [ ! -d "backend" ] || [ ! -d "frontend" ]; then
    print_error "Ce script doit être exécuté depuis la racine du projet GoldSentinel"
    print_error "Structure attendue: ./backend/ et ./frontend/"
    exit 1
fi

print_status "Vérification de l'environnement..."

# Vérifier Python
if ! command -v python &> /dev/null; then
    print_error "Python n'est pas installé ou pas dans le PATH"
    exit 1
fi

# Vérifier Node.js
if ! command -v node &> /dev/null; then
    print_error "Node.js n'est pas installé ou pas dans le PATH"
    exit 1
fi

# Vérifier npm
if ! command -v npm &> /dev/null; then
    print_error "npm n'est pas installé ou pas dans le PATH"
    exit 1
fi

print_success "Environnement vérifié"

# Préparer le backend
print_status "Préparation du backend Django..."

cd backend

# Vérifier si l'environnement virtuel existe
if [ ! -d "venv" ] && [ ! -d "../venv" ]; then
    print_warning "Aucun environnement virtuel détecté"
    print_status "Création d'un environnement virtuel..."
    python -m venv venv
    print_success "Environnement virtuel créé"
fi

# Activer l'environnement virtuel
if [ -d "venv" ]; then
    source venv/bin/activate
    print_status "Environnement virtuel activé (backend/venv)"
elif [ -d "../venv" ]; then
    source ../venv/bin/activate
    print_status "Environnement virtuel activé (./venv)"
fi

# Installer les dépendances si nécessaire
if [ ! -f "requirements_installed.flag" ]; then
    print_status "Installation des dépendances Python..."
    pip install -r requirements.txt
    touch requirements_installed.flag
    print_success "Dépendances Python installées"
fi

# Appliquer les migrations
print_status "Application des migrations..."
python manage.py migrate
print_success "Migrations appliquées"

# Vérifier si les utilisateurs de test existent
print_status "Vérification des utilisateurs de test..."
python manage.py shell -c "
from account.models import UserModel
users = UserModel.objects.filter(email__in=['<EMAIL>', '<EMAIL>', '<EMAIL>'])
if users.count() >= 3:
    print('✅ Utilisateurs de test présents')
else:
    print('⚠️  Utilisateurs de test manquants - vérifiez les migrations')
"

cd ..

# Préparer le frontend
print_status "Préparation du frontend React..."

cd frontend

# Installer les dépendances si nécessaire
if [ ! -d "node_modules" ]; then
    print_status "Installation des dépendances Node.js..."
    npm install
    print_success "Dépendances Node.js installées"
fi

cd ..

# Afficher les informations de test
print_success "Préparation terminée !"
echo ""
echo "🧪 COMPTES DE TEST DISPONIBLES:"
echo "================================"
echo "👑 Administrateur:"
echo "   Email: <EMAIL>"
echo "   Mot de passe: admin123"
echo ""
echo "👔 Responsable Régional:"
echo "   Email: <EMAIL>"
echo "   Mot de passe: password123"
echo ""
echo "🔍 Agent Analyste:"
echo "   Email: <EMAIL>"
echo "   Mot de passe: password123"
echo ""
echo "🛠️ Agent Technique:"
echo "   Email: <EMAIL>"
echo "   Mot de passe: password123"
echo ""
echo "🚶 Agent Terrain:"
echo "   Email: <EMAIL>"
echo "   Mot de passe: password123"
echo ""

# Proposer de démarrer les serveurs
echo "🚀 DÉMARRAGE DES SERVEURS:"
echo "=========================="
echo ""
echo "Choisissez une option:"
echo "1) Démarrer le backend seulement"
echo "2) Démarrer le frontend seulement" 
echo "3) Démarrer les deux (recommandé)"
echo "4) Tester l'API seulement"
echo "5) Quitter"
echo ""

read -p "Votre choix (1-5): " choice

case $choice in
    1)
        print_status "Démarrage du serveur backend..."
        cd backend
        if [ -d "venv" ]; then
            source venv/bin/activate
        elif [ -d "../venv" ]; then
            source ../venv/bin/activate
        fi
        print_success "Backend démarré sur http://localhost:8000"
        python manage.py runserver
        ;;
    2)
        print_status "Démarrage du serveur frontend..."
        cd frontend
        print_success "Frontend démarré sur http://localhost:5173"
        npm run dev
        ;;
    3)
        print_status "Démarrage des deux serveurs..."
        print_warning "Ouvrez deux terminaux et exécutez:"
        echo ""
        echo "Terminal 1 (Backend):"
        echo "cd backend && source venv/bin/activate && python manage.py runserver"
        echo ""
        echo "Terminal 2 (Frontend):"
        echo "cd frontend && npm run dev"
        echo ""
        print_success "URLs d'accès:"
        echo "- Backend API: http://localhost:8000"
        echo "- Frontend: http://localhost:5173"
        echo "- Admin Django: http://localhost:8000/admin"
        ;;
    4)
        print_status "Test de l'API..."
        if [ -f "test_authentication.py" ]; then
            print_status "Assurez-vous que le backend est démarré, puis exécutez:"
            echo "python test_authentication.py"
        else
            print_error "Script de test non trouvé"
        fi
        ;;
    5)
        print_status "Au revoir !"
        exit 0
        ;;
    *)
        print_error "Choix invalide"
        exit 1
        ;;
esac
