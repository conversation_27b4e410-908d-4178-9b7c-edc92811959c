# Generated by Django 5.2.1 on 2025-05-29 12:36

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('account', '0001_initial'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='usermodel',
            name='access_level',
        ),
        migrations.RemoveField(
            model_name='usermodel',
            name='address',
        ),
        migrations.RemoveField(
            model_name='usermodel',
            name='last_login_date',
        ),
        migrations.RemoveField(
            model_name='usermodel',
            name='secure_connection',
        ),
        migrations.AddField(
            model_name='authoritymodel',
            name='description',
            field=models.TextField(blank=True),
        ),
        migrations.AddField(
            model_name='userauthoritymodel',
            name='is_primary',
            field=models.BooleanField(default=False),
        ),
        migrations.AlterField(
            model_name='authoritymodel',
            name='name',
            field=models.CharField(choices=[('Administrateur', 'Administrateur'), ('Responsable Régional', 'Responsable Régional'), ('Agent Terrain', 'Agent Terrain'), ('Agent Technique', 'Agent Technique'), ('Agent Analyste', 'Agent Analyste')], max_length=150, unique=True),
        ),
        migrations.AlterField(
            model_name='userauthoritymodel',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='user_authorities', to='account.usermodel'),
        ),
        migrations.AlterField(
            model_name='usermodel',
            name='authorized_region',
            field=models.CharField(default='BONDOUKOU', max_length=100),
        ),
        migrations.AlterUniqueTogether(
            name='userauthoritymodel',
            unique_together={('user', 'authority')},
        ),
    ]
