# 🧪 Tests d'Authentification - GoldSentinel

## 🎯 Objectif

Tester le système d'authentification multi-rôles avec interface adaptative selon les permissions utilisateur.

## 🚀 Démarrage Rapide

### Option 1: Script automatique
```bash
./start_test_servers.sh
```

### Option 2: <PERSON>
```bash
# Terminal 1 - Backend
cd backend
source venv/bin/activate  # ou créer avec: python -m venv venv
python manage.py migrate
python manage.py runserver

# Terminal 2 - Frontend  
cd frontend
npm install  # si première fois
npm run dev
```

## 👥 Comptes de Test

| Rôle | Email | Mot de passe | Permissions |
|------|-------|--------------|-------------|
| **👑 Administrateur** | <EMAIL> | admin123 | Toutes |
| **👔 Responsable Régional** | <EMAIL> | password123 | Gestion + Stats |
| **🔍 Agent Analyste** | <EMAIL> | password123 | Analyse + Validation |
| **🛠️ Agent Technique** | <EMAIL> | password123 | Support + Logs |
| **🚶 Agent Terrain** | <EMAIL> | password123 | Investigations assignées |

## 🧪 Scénarios de Test

### Test 1: Administrateur (Accès Complet)
1. **Connexion:** <EMAIL> / admin123
2. **Vérifications:**
   - ✅ Dashboard complet (5 cartes de stats)
   - ✅ Navigation complète (toutes les pages)
   - ✅ Actions rapides: 4 boutons
   - ✅ Profil: rôle "Administrateur"
   - ✅ Notifications fonctionnelles

### Test 2: Responsable Régional (Gestion)
1. **Connexion:** <EMAIL> / password123
2. **Vérifications:**
   - ✅ Dashboard étendu (4-5 cartes)
   - ✅ Navigation: pas de "Régions"
   - ✅ Actions rapides: 3 boutons (pas d'analyse)
   - ✅ Alertes critiques visibles
   - ✅ Accès risques financiers

### Test 3: Agent Analyste (Analyse)
1. **Connexion:** <EMAIL> / password123
2. **Vérifications:**
   - ✅ Dashboard système (4 cartes)
   - ✅ Navigation: avec "Spectral"
   - ✅ Actions rapides: 2 boutons
   - ❌ Pas de risques financiers
   - ✅ Peut lancer analyses

### Test 4: Agent Technique (Support)
1. **Connexion:** <EMAIL> / password123
2. **Vérifications:**
   - ✅ Dashboard technique (4 cartes)
   - ❌ Pas d'analyse spectrale
   - ✅ Actions rapides: 1 bouton (stats)
   - ✅ Accès logs système

### Test 5: Agent Terrain (Terrain)
1. **Connexion:** <EMAIL> / password123
2. **Vérifications:**
   - ✅ Dashboard simplifié (2-3 cartes)
   - ❌ Navigation réduite
   - ❌ Pas d'actions rapides
   - ✅ Focus investigations assignées

## 🔍 Points de Contrôle Détaillés

### Interface Dashboard
- [ ] Cartes de statistiques adaptées au rôle
- [ ] Actions rapides selon permissions
- [ ] Graphiques appropriés
- [ ] Alertes critiques (si autorisé)

### Navigation
- [ ] Menu filtré selon permissions
- [ ] Pages inaccessibles masquées
- [ ] Indicateur de rôle dans sidebar
- [ ] Liens fonctionnels

### Profil Utilisateur
- [ ] Informations correctes
- [ ] Modification profil
- [ ] Changement mot de passe
- [ ] Affichage autorités

### Notifications
- [ ] Badge de compteur
- [ ] Dropdown fonctionnel
- [ ] Marquage lu/non lu
- [ ] Actualisation automatique

## 🛠️ Outils de Debug

### Panneau de Debug (Dev)
- Bouton violet en bas à droite
- Informations utilisateur détaillées
- État des permissions en temps réel
- Actions de test

### Console Browser
```javascript
// Vérifier l'utilisateur connecté
console.log(localStorage.getItem('auth_token'));

// Vérifier les permissions
// (utiliser le panneau de debug)
```

### API Testing
```bash
# Tester l'API directement
python test_authentication.py
```

## 🐛 Problèmes Courants

### Erreur: "Token invalide"
**Solution:** Vider le localStorage
```javascript
localStorage.clear();
window.location.reload();
```

### Erreur: "Permissions incorrectes"
**Cause:** Cache des permissions
**Solution:** Déconnexion/reconnexion

### Erreur: "Interface identique pour tous"
**Cause:** Conditions de permissions non appliquées
**Solution:** Vérifier usePermissions() dans les composants

### Erreur: "Navigation non filtrée"
**Cause:** getFilteredNavigation() non utilisé
**Solution:** Vérifier MainLayout.tsx

## ✅ Critères de Validation

### Fonctionnel
- [ ] Connexion/déconnexion pour tous les rôles
- [ ] Dashboard adaptatif
- [ ] Navigation filtrée
- [ ] Profil fonctionnel
- [ ] Notifications temps réel

### Sécurité
- [ ] Accès restreint selon rôles
- [ ] Tokens JWT valides
- [ ] Refresh automatique
- [ ] Déconnexion sécurisée

### UX/UI
- [ ] Interface responsive
- [ ] Animations fluides
- [ ] Messages d'erreur clairs
- [ ] Design cohérent

## 📊 Matrice de Test

| Fonctionnalité | Admin | Responsable | Analyste | Technique | Terrain |
|----------------|-------|-------------|----------|-----------|---------|
| Connexion | ✅ | ✅ | ✅ | ✅ | ✅ |
| Dashboard complet | ✅ | ✅ | ✅ | ✅ | ❌ |
| Statistiques | ✅ | ✅ | ✅ | ✅ | ❌ |
| Analyse spectrale | ✅ | ✅ | ✅ | ❌ | ❌ |
| Investigations | ✅ | ✅ | ❌ | ❌ | ✅* |
| Risques financiers | ✅ | ✅ | ❌ | ❌ | ❌ |
| Gestion régions | ✅ | ❌ | ❌ | ❌ | ❌ |

*Terrain: seulement les investigations assignées

## 🚀 Prochaines Étapes

Après validation des tests d'authentification:

1. **Module Détections** - Validation et gestion
2. **Module Alertes** - Assignation aux agents
3. **Module Investigations** - Liste et suivi
4. **Analyses Spectrales** - Google Earth Engine réel
5. **Rapports PDF** - Génération automatique

## 📞 Support

En cas de problème:
1. Vérifier les logs console (F12)
2. Utiliser le panneau de debug
3. Tester l'API avec le script Python
4. Vérifier la base de données (admin Django)
